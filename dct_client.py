#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import subprocess
import threading
import time
import sys
import uuid
import signal
import atexit

# 配置参数
SERVER_HOST = "127.0.0.1"
SERVER_PORT = 33012
HEARTBEAT_INTERVAL = 15  # 心跳间隔（秒）
HEARTBEAT_TIMEOUT = 30   # 心跳超时（秒）

# 全局变量
client_log_prefix = ""
global_stop_event = None
global_socket = None
global_threads = []

# 协议消息
MSG_C_REQUEST_VD_SMART_PREFIX = "REQUEST_VD_SMART"
MSG_C_HEARTBEAT_PING = "PY_HEARTBEAT_PING"
MSG_S_HEARTBEAT_PONG = "DSMS_HEARTBEAT_PONG"
MSG_S_HEARTBEAT_PING = "DSMS_HEARTBEAT_PING"
MSG_C_HEARTBEAT_PONG = "PY_HEARTBEAT_PONG"

def log_print(message):
    """带时间戳的日志输出"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def signal_handler(signum, frame):
    """信号处理函数"""
    log_print(f"🛑 收到信号 {signum}，正在优雅退出...")
    cleanup_and_exit()

_cleanup_called = False

def cleanup_and_exit():
    """清理资源并退出"""
    global global_stop_event, global_socket, global_threads, _cleanup_called

    # 避免重复调用
    if _cleanup_called:
        return
    _cleanup_called = True

    log_print("🧹 开始清理资源...")

    # 设置停止事件
    if global_stop_event:
        global_stop_event.set()

    # 等待线程结束
    for thread in global_threads:
        if thread and thread.is_alive():
            log_print(f"⏳ 等待线程 {thread.name} 结束...")
            thread.join(timeout=2)
            if thread.is_alive():
                log_print(f"⚠️ 线程 {thread.name} 未在超时内结束")

    # 关闭socket
    if global_socket:
        try:
            global_socket.close()
            log_print("✅ Socket连接已关闭")
        except Exception as e:
            log_print(f"⚠️ 关闭Socket时出错: {e}")

    log_print("👋 客户端已退出")
    sys.exit(0)

def auto_launch_app_via_adb(package_name, activity_name, display_id):
    """自动通过ADB命令启动应用到指定虚拟显示器"""
    try:
        # 构建完整的Activity名称
        if activity_name.startswith('.'):
            full_activity = package_name + activity_name
        else:
            full_activity = activity_name

        # 构建ADB命令
        command = [
            'adb', 'shell', 'am', 'start',
            '--display', str(display_id),
            '-n', f'{package_name}/{full_activity}'
        ]

        log_print(f"🚀 自动启动应用: {package_name}/{full_activity} -> Display {display_id}")

        # 执行命令
        result = subprocess.run(command, capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            log_print(f"✅ ADB启动成功!")
            if result.stdout.strip():
                log_print(f"📄 输出: {result.stdout.strip()}")
            return True
        else:
            log_print(f"❌ ADB启动失败 (退出码: {result.returncode})")
            if result.stdout.strip():
                log_print(f"📄 输出: {result.stdout.strip()}")
            if result.stderr.strip():
                log_print(f"❌ 错误: {result.stderr.strip()}")
            return False

    except subprocess.TimeoutExpired:
        log_print("⏰ ADB命令执行超时")
        return False
    except Exception as e:
        log_print(f"💥 ADB执行异常: {e}")
        return False

def socket_heartbeat_sender_thread_func(sock, stop_event):
    """心跳发送线程"""
    log_print("💓 心跳发送/监控线程已启动。")
    last_heartbeat_time = time.time()

    try:
        while not stop_event.is_set():
            current_time = time.time()

            # 发送心跳PING
            if current_time - last_heartbeat_time >= HEARTBEAT_INTERVAL:
                try:
                    sock.sendall((MSG_C_HEARTBEAT_PING + "\n").encode('utf-8'))
                    log_print(f"💓 发送心跳PING")
                    last_heartbeat_time = current_time
                except socket.error as e:
                    log_print(f"💥 心跳发送失败，连接已断开: {e}")
                    stop_event.set()
                    break
                except Exception as e:
                    log_print(f"💥 心跳发送异常: {e}")
                    stop_event.set()
                    break

            # 检查是否超时
            if current_time - last_heartbeat_time > HEARTBEAT_TIMEOUT:
                log_print("💥 心跳超时，连接可能已断开")
                stop_event.set()
                break

            time.sleep(1)

    except Exception as e:
        log_print(f"💥 心跳线程异常: {e}")
        stop_event.set()
    finally:
        log_print("💓 心跳发送/监控线程已结束。")

def socket_receiver_thread_func(sock, stop_event, conn_info_dict):
    """消息接收线程"""
    log_print("📡 接收线程已启动。")

    try:
        sock_file = sock.makefile('r', encoding='utf-8')

        while not stop_event.is_set():
            try:
                line = sock_file.readline()
                if not line:
                    log_print("📡 服务端关闭了连接，客户端将自动退出")
                    stop_event.set()  # 设置停止事件
                    conn_info_dict['connection_closed'] = True
                    break

                line = line.strip()
                if not line:
                    continue

                log_print(f"📨 收到服务端消息: {line}")

                # 处理不同类型的消息
                if line.startswith("DSMS_CONNECTED:"):
                    # 欢迎消息
                    if "HB" in line:
                        conn_info_dict['supports_heartbeat'] = True
                        log_print("✅ 服务端支持心跳协议")
                    if "AppLaunch" in line:
                        conn_info_dict['supports_app_launch'] = True
                        log_print("✅ 服务端支持应用启动")
                        
                elif line.startswith("VD_PENDING:"):
                    log_print("⏳ DSMS 更新: VD 创建正在进行中...")
                    
                elif line.startswith("VD_CREATED:"):
                    # VD_CREATED:internalId:displayId
                    parts = line.split(":", 2)
                    if len(parts) >= 3:
                        internal_id = parts[1]
                        display_id = parts[2]
                        conn_info_dict['display_id'] = display_id
                        conn_info_dict['internal_id'] = internal_id
                        log_print(f"✅ 虚拟显示器创建成功! Display ID: {display_id}, Internal ID: {internal_id}")
                        
                elif line.startswith("VD_REUSED:"):
                    # VD_REUSED:internalId:displayId
                    parts = line.split(":", 2)
                    if len(parts) >= 3:
                        internal_id = parts[1]
                        display_id = parts[2]
                        conn_info_dict['display_id'] = display_id
                        conn_info_dict['internal_id'] = internal_id
                        log_print(f"✅ 复用虚拟显示器成功! Display ID: {display_id}, Internal ID: {internal_id}")
                        
                elif line.startswith("APP_LAUNCH_STATUS:"):
                    # APP_LAUNCH_STATUS:reqId:status:message
                    parts = line.split(":", 3)
                    if len(parts) >= 3:
                        status = parts[2]
                        message = parts[3] if len(parts) > 3 else ""
                        if status == "OK":
                            log_print(f"✅ 应用启动成功: {message}")
                        elif status == "NEED_ADB":
                            log_print(f"🔧 服务端权限限制，客户端自动执行ADB命令启动应用...")
                            # 自动执行ADB命令
                            if conn_info_dict.get('display_id') and conn_info_dict.get('target_pkg') and conn_info_dict.get('target_activity'):
                                auto_launch_success = auto_launch_app_via_adb(
                                    conn_info_dict['target_pkg'],
                                    conn_info_dict['target_activity'],
                                    conn_info_dict['display_id']
                                )
                                if auto_launch_success:
                                    log_print(f"✅ 客户端ADB启动成功!")
                                else:
                                    log_print(f"❌ 客户端ADB启动失败")
                            else:
                                log_print(f"📋 ADB命令: {message}")
                                log_print(f"💡 请手动执行上述命令来启动应用")
                        else:
                            log_print(f"❌ 应用启动失败: {message}")
                        conn_info_dict['app_launch_status'] = status
                        
                elif line == MSG_S_HEARTBEAT_PING:
                    # 响应服务端心跳
                    try:
                        sock.sendall((MSG_C_HEARTBEAT_PONG + "\n").encode('utf-8'))
                        log_print("💓 响应服务端PING with PONG")
                    except Exception as e:
                        log_print(f"💥 响应心跳失败: {e}")
                        break
                        
                elif line == MSG_S_HEARTBEAT_PONG:
                    log_print("💓 收到服务端PONG响应")
                    
                elif line.startswith("ERROR:"):
                    log_print(f"❌ 服务端错误: {line}")
                    conn_info_dict['error'] = line
                    break

                elif line.startswith("SERVER_SHUTDOWN:"):
                    log_print(f"🛑 服务端正在关闭: {line}")
                    conn_info_dict['server_shutdown'] = True
                    stop_event.set()
                    break

                else:
                    log_print(f"📨 其他消息: {line}")
                    
            except socket.timeout:
                continue
            except socket.error as e:
                if not stop_event.is_set():
                    log_print(f"📡 Socket连接错误，服务端可能已断开: {e}")
                    stop_event.set()
                    conn_info_dict['connection_closed'] = True
                break
            except Exception as e:
                if not stop_event.is_set():
                    log_print(f"📡 接收消息异常: {e}")
                    stop_event.set()
                    conn_info_dict['connection_closed'] = True
                break

    except Exception as e:
        log_print(f"📡 接收线程异常: {e}")
        stop_event.set()
        conn_info_dict['connection_closed'] = True
    finally:
        stop_event.set()
        log_print("📡 接收线程已结束。")

def connect_and_request_vd(target_pkg_name, target_act_name, force_new_vd_flag=False):
    """连接服务端并请求虚拟显示器"""
    global global_stop_event, global_socket, global_threads

    client_id_tag = f"PyDC_{uuid.uuid4().hex[:4]}"
    log_print(f"🚀 启动 DCT 客户端 ({client_id_tag})")

    # 连接信息字典
    conn_info_dict = {
        'supports_heartbeat': False,
        'supports_app_launch': False,
        'display_id': None,
        'internal_id': None,
        'app_launch_status': None,
        'error': None,
        'target_pkg': target_pkg_name,
        'target_activity': target_act_name
    }

    stop_event = threading.Event()
    global_stop_event = stop_event  # 保存到全局变量
    local_socket = None
    heartbeat_thread_obj = None
    receiver_thread_obj = None
    
    try:
        # 建立连接
        log_print(f"🔌 连接到 {SERVER_HOST}:{SERVER_PORT}...")
        local_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        global_socket = local_socket  # 保存到全局变量
        local_socket.settimeout(10)
        local_socket.connect((SERVER_HOST, SERVER_PORT))
        local_socket.settimeout(None)
        log_print(f"✅ 已连接 DSMS。")
        
        # 启动接收线程
        receiver_thread_obj = threading.Thread(
            target=socket_receiver_thread_func,
            args=(local_socket, stop_event, conn_info_dict),
            name="ReceiverThread",
            daemon=True
        )
        receiver_thread_obj.start()
        global_threads.append(receiver_thread_obj)  # 保存到全局列表
        
        # 等待欢迎消息
        time.sleep(0.5)
        
        # 发送VD请求
        force_new_str = "force_new" if force_new_vd_flag else "reuse"
        req_message_str = f"{MSG_C_REQUEST_VD_SMART_PREFIX}:{client_id_tag}:{force_new_str}:{target_pkg_name}:{target_act_name}\n"
        
        log_print(f"📤 发送请求: {req_message_str.strip()}")
        local_socket.sendall(req_message_str.encode('utf-8'))
        
        # 等待VD创建结果
        vd_timeout = 30
        start_time = time.time()
        
        while time.time() - start_time < vd_timeout:
            if stop_event.is_set():
                break
            if conn_info_dict.get('display_id'):
                break
            if conn_info_dict.get('error'):
                break
            time.sleep(0.1)
        
        if conn_info_dict.get('error'):
            log_print(f"❌ VD创建失败: {conn_info_dict['error']}")
            return False
            
        if not conn_info_dict.get('display_id'):
            log_print("❌ VD创建超时")
            return False
            
        # 启动心跳线程
        if conn_info_dict.get('supports_heartbeat'):
            time.sleep(0.1)  # 稍微延迟启动心跳
            heartbeat_thread_obj = threading.Thread(
                target=socket_heartbeat_sender_thread_func,
                args=(local_socket, stop_event),
                name="HeartbeatThread",
                daemon=True
            )
            heartbeat_thread_obj.start()
            global_threads.append(heartbeat_thread_obj)  # 保存到全局列表
            log_print("💓 心跳线程已启动（在请求发送后）。")
        
        # 快速启动应用（减少等待时间）
        if conn_info_dict.get('supports_app_launch') and target_pkg_name and target_act_name:
            log_print(f"🚀 快速启动应用 '{target_pkg_name}' 到虚拟显示器...")

            # 先等待短时间看服务端是否能启动
            app_timeout = 3  # 减少到3秒
            start_time = time.time()

            while time.time() - start_time < app_timeout:
                if stop_event.is_set():
                    break
                if conn_info_dict.get('app_launch_status'):
                    break
                time.sleep(0.1)

            # 无论服务端是否响应，都立即尝试客户端启动
            if not conn_info_dict.get('app_launch_status'):
                log_print("⚡ 客户端立即启动应用...")
            else:
                log_print("⚡ 服务端已响应，客户端补充启动确保成功...")

            # 立即尝试启动应用
            if conn_info_dict.get('display_id'):
                auto_launch_success = auto_launch_app_via_adb(
                    target_pkg_name,
                    target_act_name,
                    conn_info_dict['display_id']
                )
                if auto_launch_success:
                    log_print(f"✅ 应用启动成功!")
                    conn_info_dict['app_launch_status'] = 'CLIENT_LAUNCH_SUCCESS'
                else:
                    log_print(f"❌ 应用启动失败")
                    conn_info_dict['app_launch_status'] = 'CLIENT_LAUNCH_FAILED'
        
        # 保持连接
        log_print("🔗 保持连接中... (按 Ctrl+C 退出或关闭Android应用自动退出)")

        try:
            while not stop_event.is_set():
                time.sleep(0.5)  # 更频繁检查退出条件

                # 检查连接是否被标记为关闭
                if conn_info_dict.get('connection_closed'):
                    log_print("🔌 检测到连接已断开，自动退出")
                    break

                # 检查服务器是否关闭
                if conn_info_dict.get('server_shutdown'):
                    log_print("🛑 检测到服务器关闭，自动退出")
                    break

                # 检查线程是否还活着
                if receiver_thread_obj and not receiver_thread_obj.is_alive():
                    log_print("📡 接收线程已结束，连接可能断开")
                    break

                if heartbeat_thread_obj and not heartbeat_thread_obj.is_alive():
                    log_print("💓 心跳线程已结束，连接可能断开")
                    break

        except KeyboardInterrupt:
            log_print("🛑 用户中断连接")
            stop_event.set()

        # 检查退出原因
        if conn_info_dict.get('server_shutdown'):
            log_print("✅ 服务器正常关闭，客户端退出")
        elif conn_info_dict.get('connection_closed'):
            log_print("✅ 连接正常断开，客户端退出")
        else:
            log_print("✅ 连接完成")

        return True
        
    except Exception as e:
        log_print(f"❌ 连接异常: {e}")
        return False
        
    finally:
        # 清理资源
        stop_event.set()
        
        if heartbeat_thread_obj and heartbeat_thread_obj.is_alive():
            heartbeat_thread_obj.join(timeout=2)
            
        if receiver_thread_obj and receiver_thread_obj.is_alive():
            receiver_thread_obj.join(timeout=2)
            
        if local_socket:
            try:
                local_socket.close()
            except:
                pass
                
        log_print(f"🏁 === DCT 客户端 ({client_id_tag}) 执行完毕 ===")

def get_user_input_dsms_ip():
    """从用户获取 DSMS 服务端 IP 或设备序列号"""
    dsms_ip_val = input(
        "请输入 DSMS 服务端 IP 或设备序列号 (默认为 'localhost'): "
    ).strip()
    return dsms_ip_val if dsms_ip_val else "localhost"

def get_user_input_port_with_random_default():
    """从用户获取 PC 转发端口，带随机默认值"""
    import random
    rand_port = random.randint(30000, 40000)
    while True:
        usr_input = input(f"请输入 PC 转发端口 (默认: {rand_port}): ").strip()
        if not usr_input:
            return rand_port
        try:
            port_val = int(usr_input)
            if 1 <= port_val <= 65535:
                return port_val
            else:
                print("❌ 端口号必须在 1 到 65535 之间。")
        except ValueError:
            print("❌ 请输入有效的数字端口号。")

def get_user_input_app_details_mandatory():
    """从用户获取必须的应用包名和活动名"""
    pkg_name = ""
    act_name = ""
    while not pkg_name:
        pkg_name = input("请输入要启动的应用包名 (必需, 例如 com.android.settings): ").strip()
        if not pkg_name:
            print("❌ 包名不能为空。")
    while not act_name:
        act_name = input(f"请输入应用 '{pkg_name}' 的活动名 (必需, 例如 .Settings): ").strip()
        if not act_name:
            print("❌ 活动名不能为空。")
    return pkg_name, act_name

def get_user_display_preference():
    """获取用户对显示分配策略的偏好"""
    print("\n📱 Display 分配策略:")
    print("1. 智能分配 (推荐) - 优先复用现有Display，如无可用则创建新的")
    print("2. 强制创建新 Display")
    while True:
        choice_val = input("请选择策略 (1-2, 默认为 1): ").strip()
        if not choice_val or choice_val == "1":
            return "smart_reuse_or_create", False
        elif choice_val == "2":
            return "smart_force_new", True
        else:
            print("❌ 无效选择。")

def check_adb_connection(target_dev_spec=None):
    """检查 ADB 连接以及目标设备是否活动"""
    log_print("🔍 检查 ADB 连接...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            log_print(f"ADB 'devices' 命令失败: {result.stderr}")
            return False

        lines_val = result.stdout.strip().split('\n')
        dev_lines = [
            ln for ln in lines_val if ln.strip() and not ln.lower().startswith('list of devices attached')
        ]
        if not dev_lines:
            log_print("未检测到 ADB 设备。")
            return False

        found_active_target = False
        active_dev_count = 0
        for ln_dev in dev_lines:
            parts_dev = ln_dev.split()
            if len(parts_dev) < 2:
                continue
            dev_id_val, dev_state_val = parts_dev[0], parts_dev[1].lower()
            if dev_state_val not in ["device", "emulator"]:
                continue
            active_dev_count += 1
            if target_dev_spec and target_dev_spec.lower() != "localhost":
                if dev_id_val == target_dev_spec:
                    found_active_target = True
                    break
            else:
                found_active_target = True

        if not found_active_target and target_dev_spec and target_dev_spec.lower() != "localhost":
            log_print(f"❌ 未找到目标设备 '{target_dev_spec}' 或其未在列出设备中激活。")
            return False
        if active_dev_count == 0:
            log_print("❌ 未检测到活动的 ADB 设备。")
            return False

        log_print(f"✅ ADB 连接正常。找到 {active_dev_count} 个活动设备。")
        return True

    except subprocess.TimeoutExpired:
        log_print("❌ ADB 命令执行超时")
        return False
    except FileNotFoundError:
        log_print("❌ 未找到 ADB 命令，请确保 Android SDK 已安装并添加到 PATH")
        return False
    except Exception as e:
        log_print(f"❌ ADB 检查异常: {e}")
        return False

def get_user_input():
    """获取用户输入并验证"""
    print("=== DCT 客户端配置 ===")
    print("连接到虚拟显示器管理服务并启动应用")
    print()

    # 获取服务器信息
    server_ip = get_user_input_dsms_ip()

    # 检查ADB连接
    if not check_adb_connection(server_ip):
        if input("⚠️ ADB 连接检查失败。是否继续？(y/n, 默认为 n): ").strip().lower() != 'y':
            log_print("👋 因 ADB 连接问题退出。")
            sys.exit(1)

    # 获取端口
    server_port = get_user_input_port_with_random_default()

    # 获取应用信息（必需）
    target_pkg, target_activity = get_user_input_app_details_mandatory()

    # 获取显示策略
    strategy_name, force_new = get_user_display_preference()

    log_print(
        f"⚙️ 用户配置: DSMS 设备='{server_ip}', "
        f"PC 端口={server_port}, 应用='{target_pkg}/{target_activity}', "
        f"策略='{strategy_name}', 强制新建={force_new}"
    )

    return {
        'server_ip': server_ip,
        'server_port': server_port,
        'target_pkg': target_pkg,
        'target_activity': target_activity,
        'force_new': force_new
    }

def confirm_configuration(config):
    """确认配置信息"""
    print()
    print("📋 === 配置确认 ===")
    print(f"🌐 服务器地址: {config['server_ip']}:{config['server_port']}")
    print(f"📱 目标应用: {config['target_pkg']}")
    print(f"🎯 Activity类: {config['target_activity']}")
    print(f"🆕 强制新建VD: {'是' if config['force_new'] else '否'}")
    print()

    while True:
        confirm = input("确认以上配置并开始连接? (Y/n): ").strip().lower()
        if confirm in ['y', 'yes', '']:
            return True
        elif confirm in ['n', 'no']:
            return False
        print("❌ 请输入 y 或 n")

def setup_adb_forwarding(pc_port, device_port, target_device_specifier):
    """设置从 PC 到设备的 ADB 端口转发"""
    log_print(
        f"正在设置 ADB 端口转发 (PC:{pc_port} -> DeviceTCP:{device_port} 给目标 '{target_device_specifier}')..."
    )

    try:
        adb_prefix_args = []
        if target_device_specifier and target_device_specifier.lower() != "localhost":
            adb_prefix_args = ["-s", target_device_specifier]

        # 首先移除 PC 端口上任何已存在的转发
        subprocess.run(['adb'] + adb_prefix_args + ["forward", "--remove", f"tcp:{pc_port}"],
                      capture_output=True, timeout=5)

        # 设置新的端口转发
        result = subprocess.run(
            ['adb'] + adb_prefix_args + ["forward", f"tcp:{pc_port}", f"tcp:{device_port}"],
            capture_output=True, text=True, timeout=5
        )

        if result.returncode == 0:
            log_print(f"端口转发 tcp:{pc_port} -> tcp:{device_port} 设置成功。")
            return True
        else:
            log_print(f"端口转发设置失败。ADB 输出: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        log_print("❌ ADB 命令执行超时")
        return False
    except FileNotFoundError:
        log_print("❌ 未找到 ADB 命令，请确保 Android SDK 已安装并添加到 PATH")
        return False
    except Exception as e:
        log_print(f"❌ ADB 设置异常: {e}")
        return False

def main():
    """主函数"""
    main_client_tag = f"PyDC_{int(time.time() % 10000)}"
    global client_log_prefix
    client_log_prefix = f"[{main_client_tag}]"

    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    atexit.register(cleanup_and_exit)

    log_print(f"🚀 === DCT 客户端 ({main_client_tag}) 启动 ===")
    log_print(f"📅 时间戳: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 获取用户输入
    config = get_user_input()
    if not config:
        print("❌ 配置获取失败")
        sys.exit(1)

    # 确认配置
    if not confirm_configuration(config):
        print("❌ 用户取消操作")
        sys.exit(0)

    # 设置ADB转发
    if not setup_adb_forwarding(config['server_port'], 33012, config['server_ip']):
        print("❌ ADB 端口转发设置失败")
        sys.exit(1)

    print()
    print(f"� === 开始连接并请求虚拟显示器 ===")

    # 更新全局配置
    global SERVER_HOST, SERVER_PORT
    SERVER_HOST = config['server_ip']
    SERVER_PORT = config['server_port']

    # 连接并请求
    try:
        success = connect_and_request_vd(
            config['target_pkg'],
            config['target_activity'],
            config['force_new']
        )

        if success:
            log_print("✅ 连接成功完成")
        else:
            log_print("❌ 连接失败")

    except KeyboardInterrupt:
        log_print("🛑 用户中断程序")
    except Exception as e:
        log_print(f"💥 程序异常: {e}")
    finally:
        cleanup_and_exit()

if __name__ == "__main__":
    main()
