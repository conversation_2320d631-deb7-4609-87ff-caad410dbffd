"""
DCT (Display Control Tool) Client
---------------------------------
此脚本作为客户端连接到 DSMS (Display Server Management Service)，
请求虚拟显示器 (VD)，可选择在其上启动应用程序，
并通过心跳 PING 维持连接。
它使用 ADB 进行端口转发到运行 DSMS 的目标 Android 设备。
"""

import socket
import subprocess
import time
import random
import sys
import threading
import queue
import select
from typing import Tuple, Optional, Dict, Any, List, Union

# --- 配置常量 ---
DSMS_DEVICE_IP_DEFAULT: str = "localhost"
DSMS_SERVICE_PORT: int = 33012
ADB_PATH: str = "adb"  # 或者你的 adb 完整路径
MIN_RANDOM_PORT: int = 10000
MAX_RANDOM_PORT: int = 20000
CLIENT_PING_INTERVAL: float = 8.0  # 秒
SERVER_ACTIVITY_TIMEOUT: float = 25.0  # 服务端 PONG 超时时间
VD_REQUEST_TIMEOUT: float = 120.0  # 获取 VD 的总超时时间
APP_LAUNCH_STATUS_TIMEOUT: float = 30.0 # 等待应用启动状态的超时时间

# --- 协议消息常量 ---
# 服务端到客户端
MSG_S_WELCOME_PREFIX: str = "DSMS_CONNECTED:OK"
MSG_S_PONG: str = "DSMS_HEARTBEAT_PONG"
MSG_S_PING: str = "DSMS_HEARTBEAT_PING"
MSG_S_VD_REUSED_PREFIX: str = "VD_REUSED:"
MSG_S_VD_CREATED_PREFIX: str = "VD_CREATED:"
MSG_S_VD_PENDING_PREFIX: str = "VD_PENDING:"
MSG_S_APP_LAUNCH_STATUS_PREFIX: str = "APP_LAUNCH_STATUS:"
MSG_S_ERROR_PREFIX: str = "ERROR:"

# 内部队列消息
Q_MSG_CONNECTION_CLOSED_BY_SERVER: str = "CONNECTION_CLOSED_BY_SERVER"
Q_MSG_CONNECTION_ERROR_RECV: str = "CONNECTION_ERROR_RECV"
Q_MSG_CONNECTION_ERROR_UNEXPECTED_RECV: str = "CONNECTION_ERROR_UNEXPECTED_RECV"

# 客户端到服务端
MSG_C_PING: str = "PY_HEARTBEAT_PING\n"
MSG_C_PONG: str = "PY_HEARTBEAT_PONG\n"
MSG_C_REQUEST_VD_SMART_PREFIX: str = "REQUEST_VD_SMART"

# --- 全局变量 ---
stop_event: threading.Event = threading.Event()
last_server_activity_time: float = 0.0
message_queue: "queue.Queue[str]" = queue.Queue()
client_log_prefix: str = ""


def log_print(message: str) -> None:
    """使用客户端日志前缀打印消息。"""
    print(f"{client_log_prefix} {message}")


# --- ADB 命令辅助函数 ---
def adb_command(command_args: List[str], timeout: float = 15.0) -> Tuple[bool, str]:
    """
    执行 ADB 命令并返回其成功状态和输出。
    处理常见的 ADB 守护进程启动消息。
    """
    try:
        full_command: List[str] = [ADB_PATH] + command_args
        result = subprocess.run(full_command, capture_output=True, text=True, check=False,
                                timeout=timeout)
        # 处理 ADB 守护进程启动消息
        if result.stdout and (
                "daemon not running" in result.stdout.lower() or
                "daemon started successfully" in result.stdout.lower()):
            time.sleep(2)
            result = subprocess.run(full_command, capture_output=True, text=True, check=False,
                                    timeout=timeout)

        if result.returncode == 0:
            if "error" in result.stderr.lower() or \
                    "fail" in result.stderr.lower() or \
                    "denied" in result.stderr.lower() or \
                    ("not found" in result.stderr.lower() and "adb server" not in result.stderr.lower()):
                if not ("List of devices attached" in result.stdout and not result.stderr.strip()):
                    return False, (result.stdout.strip() + "\n" + result.stderr.strip()).strip()
            return True, result.stdout.strip()
        else:
            error_message = f"ADB 命令执行失败 (返回码: {result.returncode})"
            full_output = (result.stdout.strip() + "\n" + result.stderr.strip()).strip()
            return False, full_output if full_output else error_message
    except subprocess.TimeoutExpired:
        return False, "ADB 命令执行超时。"
    except FileNotFoundError:
        return False, f"错误: 未在 '{ADB_PATH}' 找到 ADB 命令。"
    except Exception as e:
        return False, f"执行 ADB 命令时发生意外错误: {e}"


def setup_adb_forwarding(pc_port: int, device_port: int, target_device_specifier: str) -> bool:
    """设置从 PC 到设备的 ADB 端口转发。"""
    log_print(
        f"正在设置 ADB 端口转发 (PC:{pc_port} -> DeviceTCP:{device_port} 给目标 '{target_device_specifier}')..."
    )
    adb_prefix_args: List[str] = []
    if target_device_specifier and target_device_specifier.lower() != "localhost":
        adb_prefix_args = ["-s", target_device_specifier]

    # 首先移除 PC 端口上任何已存在的转发
    adb_command(adb_prefix_args + ["forward", "--remove", f"tcp:{pc_port}"], timeout=5)

    success, output = adb_command(
        adb_prefix_args + ["forward", f"tcp:{pc_port}", f"tcp:{device_port}"]
    )
    if success:
        log_print(f"端口转发 tcp:{pc_port} -> tcp:{device_port} 设置成功。")
        return True
    else:
        log_print(f"端口转发设置失败。ADB 输出: {output}")
        return False


# --- Socket 接收和心跳发送线程 ---
def socket_receiver_thread_func(
        current_socket: socket.socket,
        msg_q: "queue.Queue[str]",
        stop_evt_thread: threading.Event
) -> None:
    """
    线程函数，持续从 socket 接收数据并将消息放入队列。
    """
    global last_server_activity_time
    log_print("接收线程已启动。")
    buffer: str = ""
    try:
        current_socket.settimeout(1.0)
        while not stop_evt_thread.is_set():
            try:
                data_chunk: bytes = current_socket.recv(1024)
                if not data_chunk:
                    log_print("接收线程: 服务端关闭了连接 (recv 返回空)。")
                    msg_q.put(Q_MSG_CONNECTION_CLOSED_BY_SERVER)
                    stop_evt_thread.set()
                    break

                buffer += data_chunk.decode('utf-8', errors='ignore')
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    if line:
                        if line == MSG_S_PONG:
                            last_server_activity_time = time.time()
                        else:
                            msg_q.put(line)
            except socket.timeout:
                continue
            except UnicodeDecodeError as ude:
                log_print(f"接收线程解码错误: {ude}")
                buffer = ""
            except socket.error as e:
                if stop_evt_thread.is_set():
                    log_print("接收线程：socket 操作时 stop_event 已设置，可能正常退出。")
                else:
                    log_print(f"接收线程 Socket 错误: {e}")
                    msg_q.put(Q_MSG_CONNECTION_ERROR_RECV)
                    stop_evt_thread.set()
                break
            except Exception as e:
                log_print(f"接收线程发生意外错误: {e}")
                msg_q.put(Q_MSG_CONNECTION_ERROR_UNEXPECTED_RECV)
                stop_evt_thread.set()
                break
    finally:
        if not stop_evt_thread.is_set():
            stop_evt_thread.set()
        log_print("接收线程已结束。")


def socket_heartbeat_sender_thread_func(
        current_socket: socket.socket,
        stop_evt_thread: threading.Event
) -> None:
    """
    线程函数，定期发送 PING 消息并监控服务端活动。
    """
    global last_server_activity_time
    log_print("心跳发送/监控线程已启动。")
    last_server_activity_time = time.time()

    while not stop_evt_thread.is_set():
        try:
            # 检查服务端 PONG 超时
            if time.time() - last_server_activity_time > SERVER_ACTIVITY_TIMEOUT:
                log_print(
                    f"心跳线程: 服务端活动超时 (超过 {SERVER_ACTIVITY_TIMEOUT} 秒未收到 PONG)。"
                )
                stop_evt_thread.set()
                break

            # 发送 PING
            current_socket.sendall(MSG_C_PING.encode('utf-8'))

            # 等待指定间隔
            if stop_evt_thread.wait(CLIENT_PING_INTERVAL):
                break

        except socket.error as e:
            if stop_evt_thread.is_set():
                log_print("心跳线程：socket 操作时 stop_event 已设置，可能正常退出。")
            else:
                log_print(f"心跳发送线程 Socket 错误: {e}")
                stop_evt_thread.set()
            break
        except Exception as e:
            log_print(f"心跳发送线程发生意外错误: {e}")
            stop_evt_thread.set()
            break
    log_print("心跳发送/监控线程已结束。")


# --- 核心 VD 请求函数 ---
def request_virtual_display_with_app_launch(
        pc_port_to_connect: int,
        client_id_tag: str,
        target_pkg_name: str,
        target_act_name: str,
        force_new_vd_flag: bool = False
) -> Tuple[Optional[str], bool, Dict[str, Any], Optional[socket.socket], Optional[threading.Thread], Optional[threading.Thread]]:
    """
    连接到 DSMS，请求虚拟显示器，并等待应用启动状态。
    """
    global last_server_activity_time, message_queue, stop_event

    assert target_pkg_name, "目标包名不能为空。"
    assert target_act_name, "目标活动名不能为空。"

    display_id_val: Optional[str] = None
    is_reused_val: bool = False
    local_socket: Optional[socket.socket] = None
    conn_info_dict: Dict[str, Any] = {
        'start_time': time.time(),
        'protocol_used': 'smart_applaunch_required',
        'force_new_requested': force_new_vd_flag,
        'app_package_requested': target_pkg_name,
        'app_activity_requested': target_act_name,
        'server_messages': []
    }
    receiver_thread_obj: Optional[threading.Thread] = None
    heartbeat_thread_obj: Optional[threading.Thread] = None

    # 清理并重置全局变量
    stop_event.clear()
    while not message_queue.empty():
        try:
            message_queue.get_nowait()
        except queue.Empty:
            break

    try:
        local_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        local_socket.settimeout(20.0)
        log_print(
            f"通过 localhost:{pc_port_to_connect} ({conn_info_dict['protocol_used']} 协议) 连接 DSMS..."
        )
        local_socket.connect(("localhost", pc_port_to_connect))
        log_print("✅ 已连接 DSMS。")

        # 启动接收线程
        receiver_thread_obj = threading.Thread(
            target=socket_receiver_thread_func,
            args=(local_socket, message_queue, stop_event),
            name="ReceiverThread",
            daemon=True
        )
        receiver_thread_obj.start()

        # 等待欢迎消息
        try:
            welcome_msg_val: str = message_queue.get(timeout=10.0)
            conn_info_dict['server_messages'].append({
                'timestamp': time.time() - conn_info_dict['start_time'],
                'message': welcome_msg_val, 'source': 'queue_welcome'
            })
            if welcome_msg_val.startswith("CONNECTION_"):
                raise socket.error(f"连接初始化失败: {welcome_msg_val}")
            log_print(f"服务端欢迎: {welcome_msg_val}")
            if not welcome_msg_val.startswith(MSG_S_WELCOME_PREFIX):
                raise ValueError("来自服务端的欢迎消息格式无效。")
            conn_info_dict['supports_heartbeat'] = "_HB" in welcome_msg_val
            conn_info_dict['supports_app_launch'] = "_AppLaunch" in welcome_msg_val
            if not conn_info_dict['supports_app_launch']:
                raise ValueError("服务端不支持 AppLaunch 协议特性。")
        except queue.Empty:
            raise socket.timeout("超时: 未收到服务端的欢迎消息。")

        # 发送 VD 请求 (在启动心跳之前发送，避免时序问题)
        force_new_str = "force_new" if force_new_vd_flag else "reuse"
        req_message_str = f"{MSG_C_REQUEST_VD_SMART_PREFIX}:{client_id_tag}:{force_new_str}:{target_pkg_name}:{target_act_name}\n"

        # 添加调试信息
        log_print(f"📤 准备发送的消息: {repr(req_message_str)}")  # 使用 repr 显示转义字符
        log_print(f"📤 消息长度: {len(req_message_str)} 字节")
        log_print(f"📤 消息编码后长度: {len(req_message_str.encode('utf-8'))} 字节")

        if stop_event.is_set():
            raise socket.error("在发送 VD 请求前连接已关闭。")

        log_print(f"📤 发送给 DSMS: {req_message_str.strip()}")
        local_socket.sendall(req_message_str.encode('utf-8'))
        conn_info_dict['request_sent'] = req_message_str.strip()

        # 在发送请求后启动心跳线程，避免心跳消息与请求消息冲突
        if conn_info_dict.get('supports_heartbeat'):
            # 稍微延迟启动心跳，确保请求消息先被处理
            time.sleep(0.1)
            heartbeat_thread_obj = threading.Thread(
                target=socket_heartbeat_sender_thread_func,
                args=(local_socket, stop_event),
                name="HeartbeatThread",
                daemon=True
            )
            heartbeat_thread_obj.start()
            log_print("💓 心跳线程已启动（在请求发送后）。")
        else:
            log_print("ℹ️ 服务端不支持心跳协议。")

        # 等待 VD_CREATED 或 VD_REUSED
        start_wait_vd: float = time.time()
        while time.time() - start_wait_vd < VD_REQUEST_TIMEOUT:
            if stop_event.is_set():
                raise socket.error("在等待 VD 响应时连接已关闭。")
            try:
                resp_msg: str = message_queue.get(timeout=1.0)
                conn_info_dict['server_messages'].append({
                    'timestamp': time.time() - conn_info_dict['start_time'],
                    'message': resp_msg, 'source': 'queue_vd_resp'
                })
                log_print(f"📨 (VD 阶段) DSMS: {resp_msg}")

                if resp_msg.startswith(MSG_S_VD_REUSED_PREFIX):
                    parts = resp_msg.split(":", 2)
                    display_id_val, conn_info_dict['internal_client_id'] = parts[2], parts[1]
                    is_reused_val = True
                    break
                elif resp_msg.startswith(MSG_S_VD_CREATED_PREFIX):
                    parts = resp_msg.split(":", 2)
                    display_id_val, conn_info_dict['internal_client_id'] = parts[2], parts[1]
                    is_reused_val = False
                    break
                elif resp_msg.startswith(MSG_S_VD_PENDING_PREFIX):
                    log_print("⏳ DSMS 更新: VD 创建正在进行中...")
                elif resp_msg.startswith(MSG_S_ERROR_PREFIX):
                    err_detail = resp_msg.split(":", 1)[-1] if ":" in resp_msg else resp_msg
                    conn_info_dict['error'] = err_detail
                    raise socket.error(f"DSMS 返回错误: {err_detail}")
                elif resp_msg == MSG_S_PING:
                    if not stop_event.is_set() and local_socket:
                        try:
                            local_socket.sendall(MSG_C_PONG.encode('utf-8'))
                        except socket.error as se_p:
                            log_print(f"主流程发送 PONG 时出错: {se_p}")
                            stop_event.set()
                elif resp_msg.startswith("CONNECTION_"):
                    raise socket.error(f"等待 VD 响应期间连接发生问题: {resp_msg}")
            except queue.Empty:
                continue

        if display_id_val is None:
            if not stop_event.is_set():
                raise socket.timeout(f"等待 VD 响应超时。")
            else:
                raise socket.error("等待 VD 响应时连接因 stop_event 关闭。")
        conn_info_dict['result_type'] = 'reused' if is_reused_val else 'created'

        # 等待 APP_LAUNCH_STATUS
        log_print(f"⏳ 等待应用 '{target_pkg_name}' 启动状态...")
        app_launch_status_ok: bool = False
        start_wait_app: float = time.time()
        while time.time() - start_wait_app < APP_LAUNCH_STATUS_TIMEOUT:
            if stop_event.is_set():
                raise socket.error("在获取 APP_LAUNCH_STATUS 前连接已关闭。")
            try:
                resp_msg = message_queue.get(timeout=1.0)
                conn_info_dict['server_messages'].append({
                    'timestamp': time.time() - conn_info_dict['start_time'],
                    'message': resp_msg, 'source': 'queue_app_status'
                })
                log_print(f"📨 (应用启动阶段) DSMS: {resp_msg}")
                if resp_msg.startswith(MSG_S_APP_LAUNCH_STATUS_PREFIX):
                    conn_info_dict['app_launch_status'] = resp_msg
                    app_launch_status_ok = True
                    break
                elif resp_msg == MSG_S_PING:
                    if not stop_event.is_set() and local_socket:
                        try:
                            local_socket.sendall(MSG_C_PONG.encode('utf-8'))
                        except socket.error as se_p:
                            log_print(f"等待应用状态时发送 PONG 出错: {se_p}")
                            stop_event.set()
                elif resp_msg.startswith("CONNECTION_"):
                    raise socket.error(f"等待 APP_LAUNCH_STATUS 期间连接发生问题: {resp_msg}")
            except queue.Empty:
                continue

        if not app_launch_status_ok:
            if not stop_event.is_set():
                log_print("⚠️ 超时: 未收到明确的应用启动状态。")
                conn_info_dict['app_launch_status'] = "TIMEOUT_WAITING_FOR_STATUS"
            else:
                raise socket.error("等待 APP_LAUNCH_STATUS 时连接因 stop_event 关闭。")

        conn_info_dict['total_duration_to_vd_and_app_status'] = time.time() - conn_info_dict['start_time']
        return (display_id_val, is_reused_val, conn_info_dict,
                local_socket, receiver_thread_obj, heartbeat_thread_obj)

    except Exception as e_req:
        log_print(f"💥 请求 VD 或建连时错误: {e_req}")
        conn_info_dict['error'] = str(e_req)
        stop_event.set()
        if local_socket:
            try:
                local_socket.close()
            except:
                pass
        if receiver_thread_obj and receiver_thread_obj.is_alive():
            receiver_thread_obj.join(timeout=2)
        if heartbeat_thread_obj and heartbeat_thread_obj.is_alive():
            heartbeat_thread_obj.join(timeout=max(CLIENT_PING_INTERVAL + 1, 2))
        return None, False, conn_info_dict, None, None, None


# --- 用户输入函数 ---
def get_user_input_dsms_ip() -> str:
    """从用户获取 DSMS 服务端 IP 或设备序列号。"""
    dsms_ip_val = input(
        f"请输入 DSMS 服务端 IP 或设备序列号 (默认为 '{DSMS_DEVICE_IP_DEFAULT}'): "
    ).strip()
    return dsms_ip_val if dsms_ip_val else DSMS_DEVICE_IP_DEFAULT


def get_user_input_port_with_random_default() -> int:
    """从用户获取 PC 转发端口，带随机默认值。"""
    rand_port = random.randint(MIN_RANDOM_PORT, MAX_RANDOM_PORT)
    while True:
        usr_input = input(f"请输入 PC 转发端口 (默认: {rand_port}): ").strip()
        if not usr_input:
            return rand_port
        try:
            port_val = int(usr_input)
            if 1 <= port_val <= 65535:
                return port_val
            else:
                print("❌ 端口号必须在 1 到 65535 之间。")
        except ValueError:
            print("❌ 请输入有效的数字端口号。")


def get_user_input_app_details_mandatory() -> Tuple[str, str]:
    """从用户获取必须的应用包名和活动名。"""
    pkg_name: str = ""
    act_name: str = ""
    while not pkg_name:
        pkg_name = input("请输入要启动的应用包名 (必需, 例如 com.android.settings): ").strip()
        if not pkg_name:
            print("❌ 包名不能为空。")
    while not act_name:
        act_name = input(f"请输入应用 '{pkg_name}' 的活动名 (必需, 例如 .Settings): ").strip()
        if not act_name:
            print("❌ 活动名不能为空。")
    return pkg_name, act_name


def get_user_display_preference() -> Tuple[str, bool]:
    """获取用户对显示分配策略的偏好。"""
    print("\n📱 Display 分配策略:")
    print("1. 智能分配 (推荐) - 优先复用现有Display，如无可用则创建新的")
    print("2. 强制创建新 Display")
    while True:
        choice_val = input("请选择策略 (1-2, 默认为 1): ").strip()
        if not choice_val or choice_val == "1":
            return "smart_reuse_or_create", False
        elif choice_val == "2":
            return "smart_force_new", True
        else:
            print("❌ 无效选择。")


# --- 辅助打印和检查函数 ---
def print_connection_summary(conn_info: Dict[str, Any], disp_id: Optional[str], is_reused: bool) -> None:
    """打印连接尝试的摘要。"""
    print(f"\n📊 === 连接摘要 ===")
    print(f"🔌 协议: {conn_info.get('protocol_used', 'N/A')}")
    if 'smart' in conn_info.get('protocol_used', ''):
        print(f"🧠 强制新建: {'是' if conn_info.get('force_new_requested') else '否'}")
    if conn_info.get('app_package_requested'):
        print(
            f"📱 请求应用: {conn_info.get('app_package_requested')}"
            f"/{conn_info.get('app_activity_requested', '')}"
        )
    if 'app_launch_status' in conn_info:
        print(f"🚀 应用启动状态: {conn_info.get('app_launch_status')}")
    print(
        f"⏱️ 获取 VD 及应用状态耗时: {conn_info.get('total_duration_to_vd_and_app_status', 0):.2f}s"
    )
    print(
        f"💬 服务端消息 (初始阶段): "
        f"{len([m for m in conn_info.get('server_messages', []) if m.get('source','').startswith('queue')])}"
    )
    if disp_id:
        print(f"✅ 结果: 成功 {'复用' if is_reused else '创建'} Display (ID: {disp_id})")
    else:
        print(f"❌ 结果: 获取 Display 失败。")
    if 'error' in conn_info:
        print(f"💥 错误: {conn_info['error']}")
    print("=" * 50)


def check_adb_connection(target_dev_spec: Optional[str] = None) -> bool:
    """检查 ADB 连接以及目标设备是否活动。"""
    log_print("🔍 检查 ADB 连接...")
    success, output_val = adb_command(["devices"], timeout=10)
    if not success:
        log_print(f"ADB 'devices' 命令失败: {output_val}")
        return False

    lines_val: List[str] = output_val.strip().split('\n')
    dev_lines: List[str] = [
        ln for ln in lines_val if ln.strip() and not ln.lower().startswith('list of devices attached')
    ]
    if not dev_lines:
        log_print("未检测到 ADB 设备。")
        return False

    found_active_target: bool = False
    active_dev_count: int = 0
    for ln_dev in dev_lines:
        parts_dev = ln_dev.split()
        if len(parts_dev) < 2:
            continue
        dev_id_val, dev_state_val = parts_dev[0], parts_dev[1].lower()
        if dev_state_val not in ["device", "emulator"]:
            continue
        active_dev_count += 1
        if target_dev_spec and target_dev_spec.lower() != "localhost":
            if dev_id_val == target_dev_spec:
                found_active_target = True
                break
        else:
            found_active_target = True

    if not found_active_target and target_dev_spec and target_dev_spec.lower() != "localhost":
        log_print(f"❌ 未找到目标设备 '{target_dev_spec}' 或其未在列出设备中激活。")
        return False
    if active_dev_count == 0:
        log_print("❌ 未检测到活动的 ADB 设备。")
        return False

    log_print(f"✅ ADB 连接正常。找到 {active_dev_count} 个活动设备。")
    return True


# --- 主程序入口 ---
if __name__ == "__main__":
    main_client_tag: str = f"PyDC_{int(time.time() % 10000)}"
    client_log_prefix = f"[{main_client_tag}]"
    log_print(f"🚀 === DCT 客户端 ({main_client_tag}) 启动 ===")
    log_print(f"📅 时间戳: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    actual_dsms_device_ip_or_serial: str = get_user_input_dsms_ip()
    if not check_adb_connection(actual_dsms_device_ip_or_serial):
        if input("⚠️ ADB 连接检查失败。是否继续？(y/n, 默认为 n): ").strip().lower() != 'y':
            log_print("👋 因 ADB 连接问题退出。")
            sys.exit(1)

    main_pc_forwarding_port: int = get_user_input_port_with_random_default()
    main_app_package, main_app_activity = get_user_input_app_details_mandatory()
    main_selected_strategy, main_force_new = get_user_display_preference()

    log_print(
        f"⚙️ 用户配置: DSMS 设备='{actual_dsms_device_ip_or_serial}', "
        f"PC 端口={main_pc_forwarding_port}, 应用='{main_app_package}/{main_app_activity}', "
        f"策略='{main_selected_strategy}', 强制新建={main_force_new}"
    )

    if not setup_adb_forwarding(main_pc_forwarding_port, DSMS_SERVICE_PORT,
                                actual_dsms_device_ip_or_serial):
        log_print("❌ ADB 端口转发失败。程序退出。")
        sys.exit(1)

    main_active_socket: Optional[socket.socket] = None
    main_receiver_thread: Optional[threading.Thread] = None
    main_heartbeat_thread: Optional[threading.Thread] = None
    main_conn_details: Dict[str, Any] = {}

    try:
        log_print(f"🔍 === 为客户端 '{main_client_tag}' 请求虚拟显示器并启动应用 ===")
        main_target_display_id, main_was_reused, main_conn_details, \
            main_active_socket, main_receiver_thread, main_heartbeat_thread = \
            request_virtual_display_with_app_launch(
                main_pc_forwarding_port, main_client_tag,
                target_pkg_name=main_app_package,
                target_act_name=main_app_activity,
                force_new_vd_flag=main_force_new
            )

        if main_target_display_id and main_active_socket and not stop_event.is_set():
            log_print(
                f"✅ 成功 {'复用' if main_was_reused else '创建'} Display ID: {main_target_display_id}"
            )
            print_connection_summary(main_conn_details, main_target_display_id, main_was_reused)

            app_status = main_conn_details.get('app_launch_status', "")
            if MSG_S_ERROR_PREFIX in app_status or "TIMEOUT" in app_status:
                log_print(
                    f"⚠️ 应用启动可能失败 (状态: '{app_status}')。 "
                    "请检查服务端日志和应用配置。连接将保持，但应用可能未在 VD 上运行。"
                )
            else:
                log_print(f"🎉 === 任务阶段完成。连接活动。应用启动状态: '{app_status}' ===")

            print("\n按 [回车] 键关闭此客户端并释放 Display...")
            while not stop_event.is_set():
                try:
                    readable_fds: List[Any] = []
                    if sys.stdin.isatty():
                        readable_fds.append(sys.stdin)

                    ready_to_read, _, _ = select.select(readable_fds, [], [], 0.5)

                    if sys.stdin in ready_to_read:
                        _ = sys.stdin.readline().strip()
                        log_print("用户按下回车。请求关闭。")
                        stop_event.set()
                        break

                    # 处理来自服务端的消息
                    try:
                        msg_from_server: str = message_queue.get_nowait()
                        log_print(f"📨 (活动) DSMS: {msg_from_server}")
                        main_conn_details.setdefault('server_messages_active', []).append({
                            'timestamp': time.time() - main_conn_details.get('start_time', time.time()),
                            'message': msg_from_server
                        })
                        if msg_from_server == MSG_S_PING:
                            if not stop_event.is_set() and main_active_socket:
                                try:
                                    main_active_socket.sendall(MSG_C_PONG.encode('utf-8'))
                                except socket.error as se_p:
                                    log_print(f"活动循环中发送 PONG 出错: {se_p}")
                                    stop_event.set()
                        elif msg_from_server == Q_MSG_CONNECTION_CLOSED_BY_SERVER or \
                                msg_from_server.startswith("CONNECTION_ERROR"):
                            log_print(f"来自活动队列的连接问题: {msg_from_server}。正在关闭。")
                            stop_event.set()
                            break
                    except queue.Empty:
                        pass

                    if stop_event.is_set():
                        break

                except (KeyboardInterrupt, EOFError):
                    log_print("用户中断或 EOF。正在关闭...")
                    stop_event.set()
                    break
                except Exception as e_active_loop:
                    log_print(f"活动客户端循环错误: {e_active_loop}")
                    stop_event.set()
                    break
        else:
            log_print(f"❌ 未能获取 Display 或连接提前关闭。")
            print_connection_summary(main_conn_details if main_conn_details else {}, None, False)
            stop_event.set()

    except (KeyboardInterrupt, EOFError):
        log_print("\n主流程检测到用户中断或 EOF。正在关闭...")
        stop_event.set()
    except Exception as main_exec_e:
        log_print(f"💥 主执行流程发生严重错误: {main_exec_e}")
        stop_event.set()
    finally:
        log_print("ℹ️  正在关闭客户端...")
        stop_event.set()

        # 等待线程终止
        threads_to_join: List[Optional[threading.Thread]] = [main_receiver_thread, main_heartbeat_thread]
        for t_obj in threads_to_join:
            if t_obj and t_obj.is_alive():
                log_print(f"等待线程 {t_obj.name} 结束...")
                t_obj.join(timeout=max(CLIENT_PING_INTERVAL + 2.0, 5.0))
                if t_obj.is_alive():
                    log_print(f"⚠️ 线程 {t_obj.name} 未在超时内结束。")

        if main_active_socket:
            socket_fd_str = str(main_active_socket.fileno()) if hasattr(main_active_socket, 'fileno') and main_active_socket.fileno() != -1 else 'N/A'
            log_print(f"尝试关闭 socket (FD: {socket_fd_str})...")
            try:
                main_active_socket.shutdown(socket.SHUT_RDWR)
            except (socket.error, OSError) as e_shut:
                log_print(f"⚠️ Socket shutdown 出错: {e_shut}")
            try:
                main_active_socket.close()
                log_print("✅ Socket 连接已关闭。")
            except (socket.error, OSError) as e_cl_sock:
                log_print(f"⚠️ 关闭 socket 时出错: {e_cl_sock}")

        log_print(f"🏁 === DCT 客户端 ({main_client_tag}) 执行完毕 ===")
        log_print(f"📅 结束时间戳: {time.strftime('%Y-%m-%d %H:%M:%S')}")