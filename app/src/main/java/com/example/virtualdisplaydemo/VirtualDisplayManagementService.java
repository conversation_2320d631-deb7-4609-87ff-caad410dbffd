package com.example.virtualdisplaydemo;

import android.app.ActivityOptions;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.GuardedBy;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class VirtualDisplayManagementService extends Service {
    private static final String TAG = "VDMService_Smart_CN";
    private static final String NOTIFICATION_CHANNEL_ID = "VDMServiceChannel_Smart_CN";
    private static final int SERVICE_NOTIFICATION_ID = 105;
    public static final int SERVER_PORT = 33012;

    private DisplayManager mDisplayManager;
    private final Map<Integer, VirtualDisplayContainer> mActiveVirtualDisplays = new ConcurrentHashMap<>();
    private final Map<String, PendingVDRequest> mPendingVDRequests = new ConcurrentHashMap<>();
    private final AtomicInteger mInternalClientIdCounter = new AtomicInteger(1);

    private final Map<Integer, NetworkClientConnection> mActiveNetworkConnections = new ConcurrentHashMap<>();

    private ServerSocket mServerSocket;
    private ExecutorService mClientHandlingExecutor;
    private HandlerThread mServiceHandlerThread;
    private Handler mServiceHandler;
    private Handler mMainThreadHandler;

    private final IBinder mBinder = new LocalBinder();
    private @Nullable ActivityCallback mActivityCallback;
    private final Object mActivityCallbackLock = new Object();

    private static class NetworkClientConnection {
        final String userIdentifier;
        final long connectionTime;
        final String connectionId; // 确保这个ID被正确传递和使用
        NetworkClientConnection(String userIdentifier, String connectionId) {
            this.userIdentifier = userIdentifier;
            this.connectionId = connectionId;
            this.connectionTime = System.currentTimeMillis();
        }
    }

    public interface ActivityCallback {
        void onRequestNewVirtualDisplayUI(String serviceRequestId, String userIdentifier, int assignedInternalClientId);
        boolean isMediaProjectionReady();
        void requestMediaProjectionFromUser(String serviceRequestId);
        void updateManualScreenOccupationStatus(int internalClientId, @Nullable String occupyingUserIdentifier, boolean isOccupied, int displayId);
        void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientId);
        void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId);
    }

    public class LocalBinder extends Binder {
        public VirtualDisplayManagementService getService() { return VirtualDisplayManagementService.this; }
        public void setActivityCallback(@Nullable ActivityCallback callback) {
            synchronized (mActivityCallbackLock) { mActivityCallback = callback; }
            Log.d(TAG, "ActivityCallback " + (callback != null ? "已设置。" : "已清除。"));
        }
    }

    public static class VirtualDisplayContainer {
        public final VirtualDisplay virtualDisplay;
        public final Surface targetSurface;
        public final int displayId;
        public final int internalClientId;
        public final String initialUserIdentifier;
        public final boolean isManuallyCreated;
        public final long creationTimestamp;
        private final Object lock = new Object();
        @GuardedBy("lock") public boolean isOccupiedByNetworkClient;
        @GuardedBy("lock") public @Nullable String occupyingUserIdentifier;
        @GuardedBy("lock") public long lastOccupationChangeTime;
        @Nullable public String errorMessage;
        public VirtualDisplayContainer(VirtualDisplay virtualDisplay, Surface targetSurface,
                                       int internalClientId, String initialUserIdentifier, boolean isManuallyCreated) {
            this.virtualDisplay = virtualDisplay; this.targetSurface = targetSurface; this.internalClientId = internalClientId;
            this.initialUserIdentifier = initialUserIdentifier; this.isManuallyCreated = isManuallyCreated;
            this.isOccupiedByNetworkClient = false; this.occupyingUserIdentifier = null; this.creationTimestamp = System.currentTimeMillis();
            this.lastOccupationChangeTime = this.creationTimestamp;
            if (virtualDisplay != null && virtualDisplay.getDisplay() != null) { this.displayId = virtualDisplay.getDisplay().getDisplayId();
            } else { this.displayId = -1; Log.e(TAG, "VDContainer为ID=" + internalClientId + "创建，但其VD或Display对象无效。"); }
            Log.i(TAG, "VDContainer已创建: InternalID=" + internalClientId + ", 用户='" + initialUserIdentifier + "', DisplayID=" + this.displayId + ", 手动=" + isManuallyCreated);
        }
        public VirtualDisplayContainer(int internalClientId, String initialUserIdentifier, boolean isManuallyCreated, String error) {
            this.virtualDisplay = null; this.targetSurface = null; this.internalClientId = internalClientId;
            this.initialUserIdentifier = initialUserIdentifier; this.isManuallyCreated = isManuallyCreated;
            this.creationTimestamp = System.currentTimeMillis(); this.displayId = -1; this.errorMessage = error;
            Log.e(TAG, "VDContainer创建失败记录: InternalID=" + internalClientId + ", 用户='" + initialUserIdentifier + "', 手动=" + isManuallyCreated + ", 错误: " + error);
        }
        public boolean tryOccupy(String networkUserIdentifier) {
            synchronized (lock) {
                if (!isManuallyCreated) { Log.d(TAG, "tryOccupy ID " + internalClientId + " 失败: 非手动VD。"); return false; }
                if (isOccupiedByNetworkClient) { Log.d(TAG, "tryOccupy ID " + internalClientId + " 失败: 已被 '" + occupyingUserIdentifier + "' 占用。"); return false; }
                if (virtualDisplay == null || virtualDisplay.getDisplay() == null) { Log.d(TAG, "tryOccupy ID " + internalClientId + " 失败: VD对象无效。"); return false; }
                isOccupiedByNetworkClient = true; occupyingUserIdentifier = networkUserIdentifier; lastOccupationChangeTime = System.currentTimeMillis();
                Log.i(TAG, "手动VD (InternalID: " + internalClientId + ", DisplayID: " + displayId + ") 被网络用户 '" + networkUserIdentifier + "' 占用。"); return true;
            }
        }
        public void releaseOccupation() {
            synchronized (lock) {
                if (isManuallyCreated && isOccupiedByNetworkClient) {
                    Log.i(TAG, "手动VD (InternalID: " + internalClientId + ", DisplayID: " + displayId + ") 被网络用户 '" + occupyingUserIdentifier + "' 释放。变为空闲。");
                    isOccupiedByNetworkClient = false; occupyingUserIdentifier = null; lastOccupationChangeTime = System.currentTimeMillis();
                }
            }
        }
        public boolean isCurrentlyOccupiedByNetwork() { synchronized (lock) { return isOccupiedByNetworkClient; } }
        public @Nullable String getOccupyingNetworkUser() { synchronized (lock) { return occupyingUserIdentifier; } }
        public long getCreationTime() { return creationTimestamp; }
    }

    private static class PendingVDRequest {
        final String serviceRequestId; final String userIdentifier; final Socket clientSocket; final PrintWriter writer;
        final CountDownLatch completionLatch = new CountDownLatch(1);
        @GuardedBy("this") String resultingDisplayId = null; @GuardedBy("this") int assignedInternalClientId = -1;
        @GuardedBy("this") boolean mediaProjectionWasDenied = false; final boolean forceCreateNew;
        PendingVDRequest(String serviceRequestId, String userIdentifier, Socket clientSocket, PrintWriter writer, boolean forceCreateNew) {
            this.serviceRequestId = serviceRequestId; this.userIdentifier = userIdentifier; this.clientSocket = clientSocket;
            this.writer = writer; this.forceCreateNew = forceCreateNew;
        }
        synchronized void setResult(int internalClientId, @Nullable String displayId) { this.assignedInternalClientId = internalClientId; this.resultingDisplayId = displayId; this.completionLatch.countDown(); }
        synchronized void setMediaProjectionDenied() { this.mediaProjectionWasDenied = true; this.completionLatch.countDown(); }
        synchronized boolean wasMediaProjectionDenied() { return this.mediaProjectionWasDenied; }
        synchronized String getResultingDisplayId() { return this.resultingDisplayId; }
        synchronized int getAssignedInternalClientId() { return this.assignedInternalClientId; }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mDisplayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);
        mClientHandlingExecutor = Executors.newCachedThreadPool();
        mServiceHandlerThread = new HandlerThread(TAG + "_SmartBgThread");
        mServiceHandlerThread.start();
        mServiceHandler = new Handler(mServiceHandlerThread.getLooper());
        mMainThreadHandler = new Handler(Looper.getMainLooper());
        createNotificationChannel();
        startForeground(SERVICE_NOTIFICATION_ID, createServiceNotification());
        Log.i(TAG, "DSMS服务已创建并在前台运行。");
        startServerSocketListener();
    }

    private void startServerSocketListener() {
        mServiceHandler.post(() -> {
            try {
                mServerSocket = new ServerSocket(SERVER_PORT);
                Log.i(TAG, "DSMS TCP服务器已在端口 " + SERVER_PORT + " 上开始监听...");
                while (!Thread.currentThread().isInterrupted() && mServerSocket != null && !mServerSocket.isClosed()) {
                    try {
                        Socket clientSocket = mServerSocket.accept();
                        Log.i(TAG, "客户端已连接，来自: " + clientSocket.getInetAddress());
                        mClientHandlingExecutor.submit(new SmartClientSocketHandler(clientSocket));
                    } catch (SocketException se) { if (mServerSocket != null && mServerSocket.isClosed()) { Log.i(TAG, "ServerSocket已关闭，正常退出监听循环。"); break; } Log.e(TAG, "监听循环中发生SocketException: " + se.getMessage());
                    } catch (IOException ioe) { Log.e(TAG, "服务器监听循环中发生IOException: " + ioe.getMessage(), ioe); try {TimeUnit.SECONDS.sleep(1);} catch(InterruptedException ie){Thread.currentThread().interrupt();} }
                }
            } catch (IOException e) { Log.e(TAG, "无法在端口 " + SERVER_PORT + " 上启动服务器: " + e.getMessage(), e);
            } finally { Log.i(TAG, "服务器Socket监听线程结束。"); closeServerSocket(); }
        });
    }

    private void closeServerSocket() {
        try { if (mServerSocket != null && !mServerSocket.isClosed()) { mServerSocket.close(); Log.i(TAG, "ServerSocket已显式关闭。"); }
        } catch (IOException e) { Log.e(TAG, "关闭ServerSocket时发生错误: " + e.getMessage(), e); }
        mServerSocket = null;
    }

    private class SmartClientSocketHandler implements Runnable {
        private final Socket mClientSocket;
        private final String mConnectionId; // Handler级别的连接ID
        private final String mClientInfo;
        private BufferedReader mReader;
        private PrintWriter mWriter;
        private volatile boolean mIsClientAlive = true;
        private Thread mHeartbeatThread;

        private @Nullable VirtualDisplayContainer mReusedManualVDForThisClient = null;
        private @Nullable PendingVDRequest mPendingVDRequestForThisClient = null;

        SmartClientSocketHandler(Socket socket) {
            this.mClientSocket = socket;
            this.mConnectionId = UUID.randomUUID().toString().substring(0, 8);
            this.mClientInfo = socket.getRemoteSocketAddress().toString();
        }

        @Override
        public void run() {
            String serviceReqIdForVD = UUID.randomUUID().toString();
            Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 已为客户端 " + mClientInfo + " 启动。");
            try {
                mReader = new BufferedReader(new InputStreamReader(mClientSocket.getInputStream()));
                mWriter = new PrintWriter(mClientSocket.getOutputStream(), true);

                // 发送欢迎消息，包含心跳支持标识
                mWriter.println("DSMS_CONNECTED:OK:Welcome_Smart_DSMS_v1.0_CN_HB_AppLaunch");

                String clientMessage = mReader.readLine();
                if (clientMessage == null) {
                    Log.w(TAG, "客户端 " + mClientInfo + " (ConnID: " + mConnectionId + ") 在发送数据前已断开。");
                    return;
                }

                Log.i(TAG, "客户端 " + mClientInfo + " (ConnID: " + mConnectionId + ") 发送: " + clientMessage);

                // 解析请求消息
                String userIdentifierFromClient;
                boolean forceCreateNew = false;
                String targetAppPackageName = null;
                String targetAppActivityName = null;

                if (clientMessage.startsWith("REQUEST_VD_SMART:")) {
                    String content = clientMessage.substring("REQUEST_VD_SMART:".length());
                    String[] parts = content.split(":", 4);

                    userIdentifierFromClient = (parts.length > 0 && !parts[0].trim().isEmpty()) ? parts[0].trim() : "SmartUser_" + mConnectionId;
                    if (parts.length > 1) {
                        forceCreateNew = "force_new".equalsIgnoreCase(parts[1].trim());
                    }
                    if (parts.length > 2 && !parts[2].trim().isEmpty()) {
                        targetAppPackageName = parts[2].trim();
                    }
                    if (parts.length > 3 && !parts[3].trim().isEmpty()) {
                        targetAppActivityName = parts[3].trim();
                    }

                    Log.i(TAG, "ConnID: " + mConnectionId + " - 解析结果: User=" + userIdentifierFromClient +
                            ", ForceNew=" + forceCreateNew + ", Pkg=" + targetAppPackageName + ", Activity=" + targetAppActivityName);
                } else if (clientMessage.startsWith("REQUEST_VD:")) {
                    userIdentifierFromClient = clientMessage.substring("REQUEST_VD:".length()).trim();
                    if (userIdentifierFromClient.isEmpty()) userIdentifierFromClient = "LegacyUser_" + mConnectionId;
                } else {
                    Log.w(TAG, "来自 " + mClientInfo + " (ConnID: " + mConnectionId + ") 的无效请求: " + clientMessage);
                    mWriter.println("ERROR:NA:无效请求格式。");
                    return;
                }

                mPendingVDRequestForThisClient = new PendingVDRequest(serviceReqIdForVD, userIdentifierFromClient, mClientSocket, mWriter, forceCreateNew);
                mPendingVDRequests.put(serviceReqIdForVD, mPendingVDRequestForThisClient);
                handleVirtualDisplayRequest(mPendingVDRequestForThisClient);

                // 检查VD创建是否成功
                if (mPendingVDRequestForThisClient != null && mPendingVDRequestForThisClient.getResultingDisplayId() != null &&
                        !mPendingVDRequestForThisClient.getResultingDisplayId().startsWith("ERROR_") &&
                        mClientSocket != null && !mClientSocket.isClosed() && mReader != null) {

                    Log.d(TAG, "ConnID: " + mConnectionId + " - Display已分配给 " + mClientInfo + "，启动心跳并保持连接...");

                    // 启动心跳线程
                    startHeartbeatThread();

                    // 如果有应用启动请求，尝试启动应用（使用ADB方式避免权限问题）
                    Log.d(TAG, "ConnID: " + mConnectionId + " - 检查应用启动参数: pkg=" + targetAppPackageName + ", activity=" + targetAppActivityName);
                    if (targetAppPackageName != null && targetAppActivityName != null) {
                        String displayId = mPendingVDRequestForThisClient.getResultingDisplayId();
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 准备启动应用: " + targetAppPackageName + "/" + targetAppActivityName + " 到显示器 " + displayId);
                        launchAppOnDisplayViaADB(targetAppPackageName, targetAppActivityName, displayId, serviceReqIdForVD);
                    } else {
                        Log.w(TAG, "ConnID: " + mConnectionId + " - 应用启动参数不完整，跳过应用启动");
                    }

                    // 保持连接并处理消息
                    maintainConnection();

                } else {
                    Log.i(TAG, "ConnID: " + mConnectionId + " - 未能成功分配Display，或出错/超时/连接提前关闭。");
                }
            } catch (SocketException se) {
                if (se.getMessage() != null && (se.getMessage().toLowerCase().contains("socket closed") || se.getMessage().toLowerCase().contains("connection reset") || se.getMessage().toLowerCase().contains("broken pipe"))) { Log.i(TAG, "ConnID: " + mConnectionId + " - SocketException表明客户端已关闭连接: " + se.getMessage());
                } else { Log.w(TAG, "ClientSocketHandler SocketException (客户端 " + mClientInfo + ", ConnID: " + mConnectionId + "): " + se.getMessage()); }
            } catch (IOException ioe) { Log.e(TAG, "ClientSocketHandler IOException (客户端 " + mClientInfo + ", ConnID: " + mConnectionId + "): " + ioe.getMessage(), ioe);
            } catch (InterruptedException ie) { Log.w(TAG, "ClientSocketHandler (客户端 " + mClientInfo + ", ConnID: " + mConnectionId + ") 被中断: " + ie.getMessage()); Thread.currentThread().interrupt();
            } catch (Exception e) { Log.e(TAG, "ClientSocketHandler (客户端 " + mClientInfo + ", ConnID: " + mConnectionId + ") 发生未预期异常: " + e.getMessage(), e);
            } finally {
                Log.d(TAG, "ConnID: " + mConnectionId + " - 进入finally块进行清理。");

                // 停止心跳线程
                mIsClientAlive = false;
                if (mHeartbeatThread != null) {
                    mHeartbeatThread.interrupt();
                    try {
                        mHeartbeatThread.join(1000);
                    } catch (InterruptedException e) {
                        Log.w(TAG, "ConnID: " + mConnectionId + " - 等待心跳线程结束被中断");
                    }
                }

                if (mPendingVDRequestForThisClient != null) {
                    mPendingVDRequests.remove(mPendingVDRequestForThisClient.serviceRequestId);
                    if (mPendingVDRequestForThisClient.getAssignedInternalClientId() != -1 && mPendingVDRequestForThisClient.getResultingDisplayId() != null && !mPendingVDRequestForThisClient.getResultingDisplayId().startsWith("ERROR_")) {
                        VirtualDisplayContainer vdToCheck = mActiveVirtualDisplays.get(mPendingVDRequestForThisClient.getAssignedInternalClientId());
                        if (vdToCheck != null && !vdToCheck.isManuallyCreated) { // 仅网络创建的VD在此自动释放
                            Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 结束，释放其创建的网络VD (InternalID: " + vdToCheck.internalClientId + ")");
                            final int internalIdToRelease = vdToCheck.internalClientId;
                            mServiceHandler.post(() -> releaseVirtualDisplay(internalIdToRelease, false));
                        }
                    }
                }
                if (mReusedManualVDForThisClient != null) { // 手动VD被复用后，客户端断开，则释放网络占用
                    Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 为复用的手动VD (InternalID: " + mReusedManualVDForThisClient.internalClientId + ") 执行完毕。释放网络占用。");
                    mReusedManualVDForThisClient.releaseOccupation();
                    mActiveNetworkConnections.remove(mReusedManualVDForThisClient.internalClientId); // 清理网络连接记录
                    final int finalReusedInternalIdForCallback = mReusedManualVDForThisClient.internalClientId;
                    final int finalReusedDisplayIdForCallback = mReusedManualVDForThisClient.displayId;
                    mMainThreadHandler.post(() -> { ActivityCallback currentCb; synchronized (mActivityCallbackLock) { currentCb = mActivityCallback; } if (currentCb != null) { currentCb.updateManualScreenOccupationStatus(finalReusedInternalIdForCallback, null, false, finalReusedDisplayIdForCallback); } });
                }
                try { if (mWriter != null) mWriter.close(); } catch (Exception e) { /* ignore */ }
                try { if (mReader != null) mReader.close(); } catch (Exception e) { /* ignore */ }
                try { if (mClientSocket != null && !mClientSocket.isClosed()) mClientSocket.close(); } catch (IOException e) { Log.e(TAG, "关闭客户端 " + mClientInfo + " (ConnID: " + mConnectionId + ") 的Socket时出错: " + e.getMessage()); }
                Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 已为客户端 " + mClientInfo + " 执行完毕并清理。");
            }
        }

        private void startHeartbeatThread() {
            mHeartbeatThread = new Thread(() -> {
                Log.i(TAG, "ConnID: " + mConnectionId + " - 服务端心跳PING机制已启动。");
                try {
                    while (mIsClientAlive && !Thread.currentThread().isInterrupted() &&
                           mClientSocket != null && !mClientSocket.isClosed()) {

                        Thread.sleep(10000); // 10秒间隔

                        if (mWriter != null && !mWriter.checkError()) {
                            mWriter.println("DSMS_HEARTBEAT_PING");
                            Log.d(TAG, "ConnID: " + mConnectionId + " - 发送心跳PING");
                        } else {
                            Log.w(TAG, "ConnID: " + mConnectionId + " - Writer无效，停止心跳");
                            break;
                        }
                    }
                } catch (InterruptedException e) {
                    Log.i(TAG, "ConnID: " + mConnectionId + " - 心跳线程被中断");
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    Log.e(TAG, "ConnID: " + mConnectionId + " - 心跳线程异常: " + e.getMessage());
                }
                Log.i(TAG, "ConnID: " + mConnectionId + " - 服务端心跳PING机制已停止。");
            }, "HeartbeatThread-" + mConnectionId);
            mHeartbeatThread.setDaemon(true);
            mHeartbeatThread.start();
        }

        private void maintainConnection() {
            try {
                String line;
                while ((line = mReader.readLine()) != null && mIsClientAlive) {
                    if ("PY_HEARTBEAT_PING".equals(line.trim())) {
                        // 响应客户端心跳
                        if (mWriter != null && !mWriter.checkError()) {
                            mWriter.println("DSMS_HEARTBEAT_PONG");
                            Log.d(TAG, "ConnID: " + mConnectionId + " - 响应客户端PING with PONG");
                        }
                    } else if ("DSMS_HEARTBEAT_PONG".equals(line.trim())) {
                        // 客户端响应我们的心跳
                        Log.d(TAG, "ConnID: " + mConnectionId + " - 收到客户端PONG响应");
                    } else {
                        Log.d(TAG, "ConnID: " + mConnectionId + " - 收到其他消息: " + line);
                    }
                }
                Log.i(TAG, "ConnID: " + mConnectionId + " - 客户端 " + mClientInfo + " 已断开 (readLine返回null)。");
            } catch (Exception e) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - 连接维护异常: " + e.getMessage());
            } finally {
                mIsClientAlive = false;
                if (mHeartbeatThread != null) {
                    mHeartbeatThread.interrupt();
                }
            }
        }

        private void launchAppOnDisplayViaADB(String packageName, String activityName, String displayId, String reqId) {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 尝试启动应用到显示器: " + packageName + "/" + activityName + " -> Display " + displayId);

            // 直接使用Intent方式，因为shell命令在Android应用内部执行有限制
            launchAppOnDisplayViaIntent(packageName, activityName, displayId, reqId);
        }

        private void launchAppOnDisplayViaIntent(String packageName, String activityName, String displayId, String reqId) {
            // 在主线程上启动Activity（Intent方式）
            mMainThreadHandler.post(() -> {
                try {
                    Context context = VirtualDisplayManagementService.this;

                    Log.i(TAG, "ConnID: " + mConnectionId + " - 开始创建Intent: pkg=" + packageName + ", activity=" + activityName);

                    // 创建Intent
                    Intent launchIntent;
                    String fullActivityName = activityName.startsWith(".") ? packageName + activityName : activityName;

                    // 尝试使用PackageManager获取启动Intent
                    PackageManager pm = context.getPackageManager();
                    try {
                        launchIntent = pm.getLaunchIntentForPackage(packageName);
                        if (launchIntent != null) {
                            Log.d(TAG, "ConnID: " + mConnectionId + " - 使用PackageManager获取的启动Intent");
                        } else {
                            // 如果获取不到，手动创建
                            launchIntent = new Intent();
                            launchIntent.setComponent(new ComponentName(packageName, fullActivityName));
                            Log.d(TAG, "ConnID: " + mConnectionId + " - 手动创建Intent: " + fullActivityName);
                        }
                    } catch (Exception e) {
                        // 如果PackageManager失败，手动创建
                        launchIntent = new Intent();
                        launchIntent.setComponent(new ComponentName(packageName, fullActivityName));
                        Log.d(TAG, "ConnID: " + mConnectionId + " - PackageManager失败，手动创建Intent: " + fullActivityName);
                    }

                    // 设置Intent标志
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);

                    // 设置显示器ID
                    ActivityOptions options = ActivityOptions.makeBasic();
                    options.setLaunchDisplayId(Integer.parseInt(displayId));

                    Log.i(TAG, "ConnID: " + mConnectionId + " - 尝试通过Intent启动应用到显示器 " + displayId +
                            ", Component: " + launchIntent.getComponent());

                    context.startActivity(launchIntent, options.toBundle());

                    Log.i(TAG, "ConnID: " + mConnectionId + " - Intent发送成功");
                    // 将网络操作移到后台线程
                    mServiceHandler.post(() -> {
                        if (mWriter != null && !mWriter.checkError()) {
                            mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":OK:IntentSent");
                        }
                    });

                } catch (SecurityException e) {
                    Log.e(TAG, "ConnID: " + mConnectionId + " - Intent启动安全异常: " + e.getMessage());
                    // 将网络操作移到后台线程，返回ADB命令给客户端
                    final String finalPackageName = packageName;
                    final String finalActivityName = activityName;
                    final String finalDisplayId = displayId;
                    final String finalReqId = reqId;
                    mServiceHandler.post(() -> {
                        if (mWriter != null && !mWriter.checkError()) {
                            String fullActivityName = finalActivityName.startsWith(".") ? finalPackageName + finalActivityName : finalActivityName;
                            String adbCommand = "adb shell am start --display " + finalDisplayId + " -n " + finalPackageName + "/" + fullActivityName;
                            Log.i(TAG, "ConnID: " + mConnectionId + " - 发送ADB命令给客户端: " + adbCommand);
                            mWriter.println("APP_LAUNCH_STATUS:" + finalReqId + ":NEED_ADB:" + adbCommand);
                        }
                    });
                } catch (Exception e) {
                    Log.e(TAG, "ConnID: " + mConnectionId + " - Intent启动异常: " + e.getMessage());
                    // 将网络操作移到后台线程
                    mServiceHandler.post(() -> {
                        if (mWriter != null && !mWriter.checkError()) {
                            mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":ERROR:IntentException_" +
                                sanitizeErrorMessage(e.getMessage()));
                        }
                    });
                }
            });
        }

        private String sanitizeErrorMessage(String message) {
            if (message == null) return "null";
            return message.replaceAll(":", "_").replaceAll("\n", " ").replaceAll("\r", " ");
        }

        private void handleVirtualDisplayRequest(PendingVDRequest request) throws InterruptedException {
            final String userIdentifier = request.userIdentifier;
            final boolean forceCreateNew = request.forceCreateNew;
            final String serviceReqId = request.serviceRequestId;
            final PrintWriter writer = request.writer;

            ActivityCallback initialCallback; synchronized (mActivityCallbackLock) { initialCallback = mActivityCallback; }
            if (initialCallback == null) { Log.e(TAG, "VD请求 (ReqID: " + serviceReqId + "): ActivityCallback为null。"); writer.println("ERROR:" + serviceReqId + ":DSMS Activity不可用"); request.setResult(-1, "ERROR_ACTIVITY_CALLBACK_NULL"); return; }

            if (!forceCreateNew) {
                mReusedManualVDForThisClient = findAndOccupyReusableManualVD(userIdentifier);
                if (mReusedManualVDForThisClient != null) {
                    mActiveNetworkConnections.put(mReusedManualVDForThisClient.internalClientId, new NetworkClientConnection(userIdentifier, mConnectionId)); // 使用Handler的mConnectionId
                    Log.d(TAG, "已为复用的手动VD InternalID " + mReusedManualVDForThisClient.internalClientId + " 添加网络连接记录: User=" + userIdentifier + ", ConnID=" + mConnectionId);
                    Log.i(TAG, "分配 (ReqID: " + serviceReqId + ")：成功复用手动VD (InternalID: " + mReusedManualVDForThisClient.internalClientId + ") 给用户: " + userIdentifier);
                    writer.println("VD_REUSED:" + mReusedManualVDForThisClient.internalClientId + ":" + mReusedManualVDForThisClient.displayId);
                    final int finalReusedInternalId = mReusedManualVDForThisClient.internalClientId; final int finalReusedDisplayId = mReusedManualVDForThisClient.displayId; final String finalUserIdentifierForCallback = userIdentifier;
                    mMainThreadHandler.post(() -> { ActivityCallback currentCb; synchronized (mActivityCallbackLock) { currentCb = mActivityCallback; } if (currentCb != null) { currentCb.updateManualScreenOccupationStatus(finalReusedInternalId, "网络:" + finalUserIdentifierForCallback, true, finalReusedDisplayId); } });
                    request.setResult(finalReusedInternalId, String.valueOf(finalReusedDisplayId)); return;
                }
            }
            Log.i(TAG, "分配 (ReqID: " + serviceReqId + ")：" + (forceCreateNew ? "强制" : "自动") + "创建新VD给用户: " + userIdentifier);
            writer.println("VD_PENDING:" + serviceReqId + ":请求已接收，处理VD创建...");
            final int assignedInternalId = mInternalClientIdCounter.getAndIncrement(); request.assignedInternalClientId = assignedInternalId;
            ActivityCallback currentCallbackForNewVD; synchronized (mActivityCallbackLock) { currentCallbackForNewVD = mActivityCallback; }
            if (currentCallbackForNewVD == null) { Log.e(TAG, "VD请求 (ReqID: " + serviceReqId + ") 创建新VD时ActivityCallback为null。"); writer.println("ERROR:" + serviceReqId + ":DSMS Activity在处理中不可用。"); request.setResult(assignedInternalId, "ERROR_ACTIVITY_CALLBACK_NULL_FOR_NEW_VD"); return; }
            final String finalServiceReqIdForLambda = serviceReqId; final String finalUserIdentifierForLambda = userIdentifier; final int finalAssignedInternalIdForLambda = assignedInternalId;
            if (!currentCallbackForNewVD.isMediaProjectionReady()) {
                Log.i(TAG, "新VD (ReqID: " + serviceReqId + "): MP未就绪。请求授权。"); writer.println("VD_PENDING:" + serviceReqId + ":等待用户授予MP权限...");
                final ActivityCallback finalCbForMP = currentCallbackForNewVD; mMainThreadHandler.post(() -> finalCbForMP.requestMediaProjectionFromUser(finalServiceReqIdForLambda));
            } else {
                Log.i(TAG, "新VD (ReqID: " + serviceReqId + "): MP已就绪。请求Activity创建UI。");
                final ActivityCallback finalCbForUI = currentCallbackForNewVD; mMainThreadHandler.post(() -> finalCbForUI.onRequestNewVirtualDisplayUI(finalServiceReqIdForLambda, finalUserIdentifierForLambda, finalAssignedInternalIdForLambda));
            }
            Log.d(TAG, "ClientSocketHandler (ReqID: " + serviceReqId + ") 等待VD创建完成的completionLatch...");
            if (!request.completionLatch.await(120, TimeUnit.SECONDS)) { Log.e(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 等待VD创建或MP权限超时。"); writer.println("ERROR:" + serviceReqId + ":等待DisplayID或权限超时。"); request.setResult(assignedInternalId, "ERROR_TIMEOUT_WAITING_FOR_DISPLAY_OR_PERMISSION"); return; }
            if (request.wasMediaProjectionDenied()) { Log.w(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 失败: MP被用户拒绝。"); writer.println("ERROR:" + serviceReqId + ":MediaProjection权限被拒绝。");
            } else if (request.getResultingDisplayId() != null && !request.getResultingDisplayId().startsWith("ERROR_")) {
                mActiveNetworkConnections.put(request.getAssignedInternalClientId(), new NetworkClientConnection(userIdentifier, mConnectionId)); // 使用Handler的mConnectionId
                Log.d(TAG, "已为新创建的VD InternalID " + request.getAssignedInternalClientId() + " 添加网络连接记录: User=" + userIdentifier + ", ConnID=" + mConnectionId);
                Log.i(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 完成。DisplayID: " + request.getResultingDisplayId());
                writer.println("VD_CREATED:" + request.getAssignedInternalClientId() + ":" + request.getResultingDisplayId());
                final int createdInternalId = request.getAssignedInternalClientId(); final int createdDisplayId;
                try { createdDisplayId = Integer.parseInt(request.getResultingDisplayId()); } catch (NumberFormatException e) { Log.e(TAG, "无法解析新创建的DisplayID: " + request.getResultingDisplayId(), e); return; }
                final String finalUserForNewNetScreen = userIdentifier;
                mMainThreadHandler.post(() -> { ActivityCallback currentCb; synchronized (mActivityCallbackLock) { currentCb = mActivityCallback; } if (currentCb != null) { currentCb.updateManualScreenOccupationStatus(createdInternalId, "网络:" + finalUserForNewNetScreen, true, createdDisplayId); } });
            } else { String errMsg = request.getResultingDisplayId() != null ? request.getResultingDisplayId() : "未知内部错误导致VD创建失败。"; Log.e(TAG, "新VD请求 (ReqID: " + serviceReqId + ") 处理出错: " + errMsg); writer.println("ERROR:" + serviceReqId + ":" + errMsg); }
        }
    }

    private synchronized @Nullable VirtualDisplayContainer findAndOccupyReusableManualVD(String networkUserIdentifier) {
        Log.d(TAG, "正在为网络用户 '" + networkUserIdentifier + "' 查找可复用的手动VD (最早优先)...");
        VirtualDisplayContainer earliestAvailable = null;
        for (VirtualDisplayContainer vdContainer : mActiveVirtualDisplays.values()) {
            if (vdContainer.isManuallyCreated && vdContainer.virtualDisplay != null && vdContainer.virtualDisplay.getDisplay() != null && !vdContainer.isCurrentlyOccupiedByNetwork()) {
                if (earliestAvailable == null || vdContainer.getCreationTime() < earliestAvailable.getCreationTime()) { earliestAvailable = vdContainer; }
            }
        }
        if (earliestAvailable != null) { if (earliestAvailable.tryOccupy(networkUserIdentifier)) { Log.i(TAG, "找到并成功占用最早的手动VD: InternalID=" + earliestAvailable.internalClientId + " 给用户 '" + networkUserIdentifier + "'"); return earliestAvailable; } else { Log.w(TAG, "找到最早手动VD (InternalID: " + earliestAvailable.internalClientId + ") 但占用失败。"); } }
        Log.i(TAG, "扫描完成：没有找到可供用户 '" + networkUserIdentifier + "' 复用的、空闲且有效的手动VD。"); return null;
    }

    public boolean isDisplayBeingUsedByNetworkClient(int internalClientId) { return mActiveNetworkConnections.containsKey(internalClientId); }

    public @Nullable String getDisplayNetworkUserInfo(int internalClientId) {
        NetworkClientConnection connection = mActiveNetworkConnections.get(internalClientId);
        if (connection != null) {
            long connectedDurationMs = System.currentTimeMillis() - connection.connectionTime;
            String connIdPart = "N/A";
            if (connection.connectionId != null && !connection.connectionId.isEmpty()) {
                connIdPart = connection.connectionId.length() >= 4 ? connection.connectionId.substring(0, 4) : connection.connectionId;
            }
            String userInfo = connection.userIdentifier + " (ConnID: " + connIdPart + ", " + (connectedDurationMs / 1000) + "s)";
            Log.d(TAG, "getDisplayNetworkUserInfo for ID " + internalClientId + ": " + userInfo + " (Full ConnID: " + connection.connectionId + ")");
            return userInfo;
        }
        Log.d(TAG, "getDisplayNetworkUserInfo for ID " + internalClientId + ": No active connection found."); return null;
    }

    public void notifyVirtualDisplayProcessed(String serviceRequestId, int internalClientId, @Nullable String displayIdOrError) { PendingVDRequest request = mPendingVDRequests.get(serviceRequestId); if (request != null) { Log.i(TAG, "服务接收到VD处理完成通知: ReqID=" + serviceRequestId + ", InternalID=" + internalClientId + ", 结果=" + displayIdOrError); request.setResult(internalClientId, displayIdOrError); } else { Log.w(TAG, "服务接收到VD处理通知，但未找到对应的挂起请求ID: " + serviceRequestId); } }
    public void notifyMediaProjectionDenied(String serviceRequestId) { PendingVDRequest request = mPendingVDRequests.get(serviceRequestId); if (request != null) { Log.w(TAG, "服务接收到MediaProjection被拒绝通知: ReqID=" + serviceRequestId); request.setMediaProjectionDenied(); } else { Log.w(TAG, "服务接收到MP被拒绝通知，但未找到对应的挂起请求ID: " + serviceRequestId); } }
    public void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientIdFromActivity) { PendingVDRequest request = mPendingVDRequests.get(serviceRequestId); if (request != null) { Log.i(TAG, "MP权限已获取，尝试重新处理请求: " + serviceRequestId); ActivityCallback currentCb; synchronized (mActivityCallbackLock) { currentCb = mActivityCallback; } if (currentCb != null) { int idToUseInUiRequest = assignedInternalClientIdFromActivity; if (idToUseInUiRequest == -1) { if (request.assignedInternalClientId == -1) { request.assignedInternalClientId = mInternalClientIdCounter.getAndIncrement(); } idToUseInUiRequest = request.assignedInternalClientId; } else { request.assignedInternalClientId = idToUseInUiRequest; } final int finalIdToUseInUiRequest = idToUseInUiRequest; final String finalServiceRequestId = serviceRequestId; final String finalUserIdentifier = userIdentifier; final ActivityCallback finalCb = currentCb; mMainThreadHandler.post(() -> finalCb.onRequestNewVirtualDisplayUI(finalServiceRequestId, finalUserIdentifier, finalIdToUseInUiRequest)); } else { Log.e(TAG, "MP就绪后重试请求 " + serviceRequestId + " 失败，ActivityCallback为null。"); request.setResult(request.assignedInternalClientId, "ERROR_CALLBACK_NULL_ON_MP_RETRY"); } } else { Log.w(TAG, "MP就绪后尝试重试请求，但未找到挂起请求: " + serviceRequestId); } }
    public void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId) { PendingVDRequest request = mPendingVDRequests.get(newRequestId); if (request != null) { Log.w(TAG, "MP请求 " + newRequestId + " 失败，因为已有请求 " + existingRequestId + " 正在等待MP权限。"); if (request.writer != null) { request.writer.println("ERROR:" + newRequestId + ":另一个屏幕权限请求正在处理中，请稍后。");} request.setResult(request.assignedInternalClientId, "ERROR_MP_REQUEST_BUSY"); } }

    public @Nullable VirtualDisplayContainer createVirtualDisplayForClientWithProjection(
            int internalClientId, @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection, @NonNull String initialUserIdentifier, boolean isManual) {
        if (mDisplayManager == null || !targetSurface.isValid()) { Log.e(TAG, "创建VD的前提条件未满足。InternalID=" + internalClientId); return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "DisplayManager或Surface无效"); }
        String sanitizedId = initialUserIdentifier.replaceAll("[^a-zA-Z0-9_-]", "_"); String vdName = (isManual ? "ManualVD_" : "NetVD_") + sanitizedId + "_" + internalClientId + "_" + (System.currentTimeMillis()%10000);
        DisplayMetrics metrics = getResources().getDisplayMetrics(); int width = Math.max(1280, metrics.widthPixels / 2); int height = Math.max(720, (width * 9) / 16); int densityDpi = metrics.densityDpi;
        int vdFlags = DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC; if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) { vdFlags |= (1 << 6); /* SECURE FLAG */ }
        VirtualDisplay virtualDisplayInstance = null;
        try { Log.i(TAG, "正在尝试创建VD: " + vdName + " (手动=" + isManual + ", 大小=" + width + "x" + height + ")"); virtualDisplayInstance = mediaProjection.createVirtualDisplay(vdName, width, height, densityDpi, vdFlags, targetSurface, null, null);
        } catch (SecurityException se) { Log.e(TAG, "创建VD'" + vdName + "' 时发生SecurityException: " + se.getMessage(), se); return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "MediaProjection安全异常: " + se.getMessage());
        } catch (Exception e) { Log.e(TAG, "创建VD'" + vdName + "' 时发生异常: " + e.getMessage(), e); return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "创建VD时异常: " + e.getMessage()); }
        if (virtualDisplayInstance != null && virtualDisplayInstance.getDisplay() != null) {
            VirtualDisplayContainer container = new VirtualDisplayContainer(virtualDisplayInstance, targetSurface, internalClientId, initialUserIdentifier, isManual);
            if (container.displayId == -1) { Log.e(TAG, "VD '" + vdName + "' 已创建，但DisplayID无效。释放此VD。"); if(virtualDisplayInstance!=null) virtualDisplayInstance.release(); return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "获取的DisplayID无效"); }
            mActiveVirtualDisplays.put(internalClientId, container); Log.i(TAG, "VD创建成功: " + vdName + " (DisplayID: " + container.displayId + ")"); return container;
        } else { Log.e(TAG, "创建VD '" + vdName + "' 失败或未能获取Display对象。"); if (virtualDisplayInstance != null) virtualDisplayInstance.release(); return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "创建VD失败或Display对象为空"); }
    }

    public void releaseVirtualDisplay(int internalClientId, boolean calledFromSurfaceDestroyed) {
        final int finalInternalClientId = internalClientId; final boolean finalCalledFromSurfaceDestroyed = calledFromSurfaceDestroyed;
        mServiceHandler.post(() -> {
            VirtualDisplayContainer container = mActiveVirtualDisplays.remove(finalInternalClientId);
            if (container == null) { Log.w(TAG, "尝试释放VD (InternalID: " + finalInternalClientId + "), 但未在活动列表中找到。"); return; }
            Log.i(TAG, "请求释放VD (InternalID: " + finalInternalClientId + "), 用户='" + container.initialUserIdentifier + "', 手动=" + container.isManuallyCreated + ", SurfaceDestroy调用=" + finalCalledFromSurfaceDestroyed);
            NetworkClientConnection removedConnection = mActiveNetworkConnections.remove(finalInternalClientId);
            if (removedConnection != null) { Log.i(TAG, "已清理VD " + finalInternalClientId + " 的网络连接记录，用户: " + removedConnection.userIdentifier); }
            if (container.virtualDisplay != null) { try { container.virtualDisplay.release(); Log.d(TAG, "VD (InternalID: " + finalInternalClientId + ", DisplayID: " + container.displayId + ") 的VD对象已释放。"); } catch (Exception e) { Log.e(TAG, "释放VD " + finalInternalClientId + " 的VD对象时异常: " + e.getMessage()); } }
            if (container.isManuallyCreated && !finalCalledFromSurfaceDestroyed) { // 如果是手动创建的，并且不是因为Surface销毁（例如是网络客户端断开）
                if (container.isCurrentlyOccupiedByNetwork()) { // 如果它曾被网络占用
                    container.releaseOccupation(); // 释放网络占用状态
                    final int finalDisplayIdForCallback = container.displayId;
                    mMainThreadHandler.post(() -> { ActivityCallback currentCb; synchronized (mActivityCallbackLock) { currentCb = mActivityCallback; } if (currentCb != null) { currentCb.updateManualScreenOccupationStatus(finalInternalClientId, null, false, finalDisplayIdForCallback); } });
                }
                mActiveVirtualDisplays.put(finalInternalClientId, container); // 将手动屏幕重新放回，因为它只是释放了网络占用，屏幕本身还在
                Log.i(TAG, "手动VD " + finalInternalClientId + " 释放了网络占用（如果曾有），但屏幕保留在活动列表。");
            } else { // 网络创建的VD，或者手动创建的VD因Surface销毁而被释放
                Log.i(TAG, "VD " + finalInternalClientId + " 已从活动列表彻底移除。");
            }
        });
    }

    @Override public int onStartCommand(Intent intent, int flags, int startId) { return START_STICKY; }
    @Override public IBinder onBind(Intent intent) { return mBinder; }
    @Override public boolean onUnbind(Intent intent) { synchronized (mActivityCallbackLock) { mActivityCallback = null; } Log.d(TAG, "Activity解绑，回调已清除。"); return super.onUnbind(intent); }
    @Override public void onDestroy() {
        Log.i(TAG, "DSMS服务正在销毁..."); closeServerSocket();
        if (mClientHandlingExecutor != null && !mClientHandlingExecutor.isShutdown()) { mClientHandlingExecutor.shutdownNow(); try { if (!mClientHandlingExecutor.awaitTermination(5, TimeUnit.SECONDS)) { Log.w(TAG, "客户端处理线程池在5秒内未能终止。"); } } catch (InterruptedException e) { mClientHandlingExecutor.shutdownNow(); Thread.currentThread().interrupt(); } }
        Log.i(TAG, "正在释放所有剩余的虚拟显示器..."); for (Integer id : new ConcurrentHashMap<>(mActiveVirtualDisplays).keySet()) { releaseVirtualDisplay(id, true); } // true表示因服务销毁
        mActiveVirtualDisplays.clear(); mActiveNetworkConnections.clear(); mPendingVDRequests.clear();
        if (mServiceHandlerThread != null) { mServiceHandlerThread.quitSafely(); try {mServiceHandlerThread.join(1000);}catch(InterruptedException e){Log.w(TAG,"服务后台线程join被中断");} }
        stopForeground(true); Log.i(TAG, "DSMS服务已完全销毁。"); super.onDestroy();
    }
    private void createNotificationChannel() { if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) { NotificationChannel sc = new NotificationChannel(NOTIFICATION_CHANNEL_ID, "DSMS前台服务", NotificationManager.IMPORTANCE_LOW); getSystemService(NotificationManager.class).createNotificationChannel(sc); } }
    private Notification createServiceNotification() { Intent ni = new Intent(this, MainActivity.class); ni.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK); int pif = PendingIntent.FLAG_UPDATE_CURRENT | (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? PendingIntent.FLAG_IMMUTABLE : 0); PendingIntent pi = PendingIntent.getActivity(this, 0, ni, pif); return new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID).setContentTitle("DSMS活动中").setContentText("正在管理虚拟显示器").setSmallIcon(R.mipmap.ic_launcher).setContentIntent(pi).setOngoing(true).build(); }
}