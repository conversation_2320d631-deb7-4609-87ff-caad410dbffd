package com.example.virtualdisplaydemo;

import android.app.ActivityOptions;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.projection.MediaProjection;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.IBinder;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.GuardedBy;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.io.OutputStreamWriter;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import android.content.ActivityNotFoundException;
import android.content.pm.PackageManager;


public class VirtualDisplayManagementService extends Service {
    private static final String TAG = "VDMService_Smart_CN";
    private static final String NOTIFICATION_CHANNEL_ID = "VDMServiceChannel_Smart_CN";
    private static final int SERVICE_NOTIFICATION_ID = 105;
    public static final int SERVER_PORT = 33012;

    private DisplayManager mDisplayManager;
    private final Map<Integer, VirtualDisplayContainer> mActiveVirtualDisplays = new ConcurrentHashMap<>();
    private final Map<String, PendingVDRequest> mPendingVDRequests = new ConcurrentHashMap<>();
    private final AtomicInteger mInternalClientIdCounter = new AtomicInteger(1);

    private final Map<Integer, NetworkClientConnection> mActiveNetworkConnections = new ConcurrentHashMap<>();

    private ServerSocket mServerSocket;
    private ExecutorService mClientHandlingExecutor;
    private HandlerThread mServiceHandlerThread;
    private Handler mServiceHandler;
    private Handler mMainThreadHandler;

    private final IBinder mBinder = new LocalBinder();
    private @Nullable ActivityCallback mActivityCallback;
    private final Object mActivityCallbackLock = new Object();

    // --- 内部类定义 ---
    private static class NetworkClientConnection {
        final String userIdentifier;
        final long connectionTime;
        final String connectionId;

        NetworkClientConnection(String userIdentifier, String connectionId) {
            this.userIdentifier = userIdentifier;
            this.connectionId = connectionId;
            this.connectionTime = System.currentTimeMillis();
        }
    }

    public interface ActivityCallback {
        void onRequestNewVirtualDisplayUI(String serviceRequestId, String userIdentifier, int assignedInternalClientId);

        boolean isMediaProjectionReady();

        void requestMediaProjectionFromUser(String serviceRequestId);

        void updateManualScreenOccupationStatus(int internalClientId, @Nullable String occupyingUserIdentifier, boolean isOccupied, int displayId);

        void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientId);

        void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId);
    }

    public class LocalBinder extends Binder {
        public VirtualDisplayManagementService getService() {
            return VirtualDisplayManagementService.this;
        }

        public void setActivityCallback(@Nullable ActivityCallback callback) {
            synchronized (mActivityCallbackLock) {
                mActivityCallback = callback;
            }
            Log.d(TAG, "ActivityCallback " + (callback != null ? "已设置。" : "已清除。"));
        }
    }

    public static class VirtualDisplayContainer {
        public final VirtualDisplay virtualDisplay;
        public final Surface targetSurface;
        public final int displayId;
        public final int internalClientId;
        public final String initialUserIdentifier;
        public final boolean isManuallyCreated;
        public final long creationTimestamp;
        private final Object lock = new Object();
        @GuardedBy("lock")
        public boolean isOccupiedByNetworkClient;
        @GuardedBy("lock")
        public @Nullable String occupyingUserIdentifier;
        @GuardedBy("lock")
        public long lastOccupationChangeTime;
        @Nullable
        public String errorMessage;

        public VirtualDisplayContainer(VirtualDisplay virtualDisplay, Surface targetSurface,
                                       int internalClientId, String initialUserIdentifier, boolean isManuallyCreated) {
            this.virtualDisplay = virtualDisplay;
            this.targetSurface = targetSurface;
            this.internalClientId = internalClientId;
            this.initialUserIdentifier = initialUserIdentifier;
            this.isManuallyCreated = isManuallyCreated;
            this.isOccupiedByNetworkClient = false;
            this.occupyingUserIdentifier = null;
            this.creationTimestamp = System.currentTimeMillis();
            this.lastOccupationChangeTime = this.creationTimestamp;
            if (virtualDisplay != null && virtualDisplay.getDisplay() != null) {
                this.displayId = virtualDisplay.getDisplay().getDisplayId();
            } else {
                this.displayId = -1;
                Log.e(TAG, "VDContainer为ID=" + internalClientId + "创建，但其VD或Display对象无效。");
            }
            Log.i(TAG, "VDContainer已创建: InternalID=" + internalClientId + ", 用户='" + initialUserIdentifier + "', DisplayID=" + this.displayId + ", 手动=" + isManuallyCreated);
        }

        public VirtualDisplayContainer(int internalClientId, String initialUserIdentifier, boolean isManuallyCreated, String error) {
            this.virtualDisplay = null;
            this.targetSurface = null;
            this.internalClientId = internalClientId;
            this.initialUserIdentifier = initialUserIdentifier;
            this.isManuallyCreated = isManuallyCreated;
            this.creationTimestamp = System.currentTimeMillis();
            this.displayId = -1;
            this.errorMessage = error;
            Log.e(TAG, "VDContainer创建失败记录: InternalID=" + internalClientId + ", 用户='" + initialUserIdentifier + "', 手动=" + isManuallyCreated + ", 错误: " + error);
        }

        public boolean tryOccupy(String networkUserIdentifier) {
            synchronized (lock) {
                if (!isManuallyCreated) {
                    Log.w(TAG, "尝试占用非手动VD (InternalID: " + internalClientId + ")，不允许。");
                    return false;
                }
                if (isOccupiedByNetworkClient) {
                    Log.w(TAG, "尝试占用已占用VD (InternalID: " + internalClientId + " by " + occupyingUserIdentifier + ")，不允许。");
                    return false;
                }
                if (virtualDisplay == null || virtualDisplay.getDisplay() == null) {
                    Log.w(TAG, "尝试占用VD (InternalID: " + internalClientId + ") 但其VD或Display对象无效，不允许。");
                    return false;
                }
                isOccupiedByNetworkClient = true;
                occupyingUserIdentifier = networkUserIdentifier;
                lastOccupationChangeTime = System.currentTimeMillis();
                Log.i(TAG, "手动VD (InternalID: " + internalClientId + ") 被网络用户 '" + networkUserIdentifier + "' 占用。");
                return true;
            }
        }

        public void releaseOccupation() {
            synchronized (lock) {
                if (isManuallyCreated && isOccupiedByNetworkClient) {
                    Log.i(TAG, "手动VD (InternalID: " + internalClientId + ") 被网络用户 '" + occupyingUserIdentifier + "' 释放。");
                    isOccupiedByNetworkClient = false;
                    occupyingUserIdentifier = null;
                    lastOccupationChangeTime = System.currentTimeMillis();
                }
            }
        }

        public boolean isCurrentlyOccupiedByNetwork() {
            synchronized (lock) {
                return isOccupiedByNetworkClient;
            }
        }

        public @Nullable String getOccupyingNetworkUser() {
            synchronized (lock) {
                return occupyingUserIdentifier;
            }
        }

        public long getCreationTime() {
            return creationTimestamp;
        }
    }

    private static class PendingVDRequest {
        final String serviceRequestId;
        final String userIdentifier;
        final Socket clientSocket;
        final PrintWriter writer;
        final CountDownLatch completionLatch = new CountDownLatch(1);
        @GuardedBy("this")
        String resultingDisplayId = null;
        @GuardedBy("this")
        int assignedInternalClientId = -1;
        @GuardedBy("this")
        boolean mediaProjectionWasDenied = false;
        final boolean forceCreateNew;

        PendingVDRequest(String serviceRequestId, String userIdentifier, Socket clientSocket, PrintWriter writer, boolean forceCreateNew) {
            this.serviceRequestId = serviceRequestId;
            this.userIdentifier = userIdentifier;
            this.clientSocket = clientSocket;
            this.writer = writer;
            this.forceCreateNew = forceCreateNew;
        }

        synchronized void setResult(int internalClientId, @Nullable String displayId) {
            this.assignedInternalClientId = internalClientId;
            this.resultingDisplayId = displayId;
            this.completionLatch.countDown();
        }

        synchronized void setMediaProjectionDenied() {
            this.mediaProjectionWasDenied = true;
            this.completionLatch.countDown();
        }

        synchronized boolean wasMediaProjectionDenied() {
            return this.mediaProjectionWasDenied;
        }

        synchronized String getResultingDisplayId() {
            return this.resultingDisplayId;
        }

        synchronized int getAssignedInternalClientId() {
            return this.assignedInternalClientId;
        }
    }
    // --- End of inner class definitions ---

    @Override
    public void onCreate() {
        super.onCreate();
        mDisplayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);
        mClientHandlingExecutor = Executors.newCachedThreadPool();
        mServiceHandlerThread = new HandlerThread(TAG + "_BgThread");
        mServiceHandlerThread.start();
        mServiceHandler = new Handler(mServiceHandlerThread.getLooper());
        mMainThreadHandler = new Handler(Looper.getMainLooper());
        createNotificationChannel();
        startForeground(SERVICE_NOTIFICATION_ID, createServiceNotification());
        Log.i(TAG, "DSMS服务已创建并在前台运行。");
        startServerSocketListener();
    }

    private void startServerSocketListener() {
        mServiceHandler.post(() -> {
            try {
                mServerSocket = new ServerSocket(SERVER_PORT);
                Log.i(TAG, "DSMS TCP服务器已在端口 " + SERVER_PORT + " 上开始监听...");
                while (!Thread.currentThread().isInterrupted() && mServerSocket != null && !mServerSocket.isClosed()) {
                    try {
                        Socket clientSocket = mServerSocket.accept();
                        Log.i(TAG, "客户端已连接，来自: " + clientSocket.getInetAddress());
                        mClientHandlingExecutor.submit(new SmartClientSocketHandler(clientSocket));
                    } catch (SocketException se) {
                        if (mServerSocket != null && mServerSocket.isClosed()) {
                            Log.i(TAG, "ServerSocket已关闭，正常退出监听循环。");
                            break;
                        }
                        Log.e(TAG, "监听循环中发生SocketException: " + se.getMessage());
                    } catch (IOException ioe) {
                        Log.e(TAG, "服务器监听循环中发生IOException: " + ioe.getMessage(), ioe);
                        try {
                            TimeUnit.SECONDS.sleep(1);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            } catch (IOException e) {
                Log.e(TAG, "无法在端口 " + SERVER_PORT + " 上启动服务器: " + e.getMessage(), e);
            } finally {
                Log.i(TAG, "服务器Socket监听线程结束。");
                closeServerSocket();
            }
        });
    }

    private void closeServerSocket() {
        try {
            if (mServerSocket != null && !mServerSocket.isClosed()) {
                mServerSocket.close();
                Log.i(TAG, "ServerSocket已显式关闭。");
            }
        } catch (IOException e) {
            Log.e(TAG, "关闭ServerSocket时发生错误: " + e.getMessage(), e);
        }
        mServerSocket = null;
    }

    // ========================================================================================
    // SmartClientSocketHandler - 客户端连接处理器 (包含心跳和应用启动逻辑)
    // ========================================================================================
    private class SmartClientSocketHandler implements Runnable {
        private final Socket mClientSocket;
        private final String mConnectionId;
        private final String mClientInfo;

        private PrintWriter mWriter;
        private BufferedReader mReader;

        private @Nullable VirtualDisplayContainer mReusedManualVDForThisClient = null;
        private @Nullable PendingVDRequest mPendingVDRequestForThisClient = null;

        private final AtomicBoolean mIsClientAlive = new AtomicBoolean(true);
        private final AtomicLong mLastClientPongTime = new AtomicLong(0);
        private ScheduledFuture<?> mServicePingTask;
        private final ScheduledExecutorService mHeartbeatScheduler = Executors.newSingleThreadScheduledExecutor();

        private static final long HEARTBEAT_INTERVAL_MS = 10000; // 服务端PING客户端的间隔
        private static final int CLIENT_PONG_TIMEOUT_MS = 25000; // 客户端PONG的超时时间

        SmartClientSocketHandler(Socket socket) {
            this.mClientSocket = socket;
            this.mConnectionId = UUID.randomUUID().toString().substring(0, 8);
            this.mClientInfo = socket.getRemoteSocketAddress().toString();
        }

        private void startHeartbeatMechanism() {
            if (mWriter == null || mHeartbeatScheduler.isShutdown() || !mIsClientAlive.get())
                return;
            mLastClientPongTime.set(System.currentTimeMillis());

            mServicePingTask = mHeartbeatScheduler.scheduleWithFixedDelay(() -> {
                if (!mIsClientAlive.get() || mClientSocket.isClosed()) {
                    stopHeartbeatMechanism(false);
                    return;
                }
                try {
                    if (System.currentTimeMillis() - mLastClientPongTime.get() > CLIENT_PONG_TIMEOUT_MS) {
                        Log.w(TAG, "ConnID: " + mConnectionId + " - 客户端PONG超时 (" +
                                (System.currentTimeMillis() - mLastClientPongTime.get()) + "ms), 认为已断开。");
                        mIsClientAlive.set(false);
                        try {
                            mClientSocket.close();
                        } catch (IOException e) {
                            Log.e(TAG, "ConnID: " + mConnectionId + " PONG超时后关闭socket异常: " + e.getMessage());
                        }
                        return;
                    }
                    mWriter.println("DSMS_HEARTBEAT_PING");
                    if (mWriter.checkError()) {
                        Log.e(TAG, "ConnID: " + mConnectionId + " - 发送PING时发生IO错误，客户端可能已断开。");
                        mIsClientAlive.set(false);
                        try {
                            mClientSocket.close();
                        } catch (IOException e) {
                            Log.e(TAG, "ConnID: " + mConnectionId + " PING IO错误后关闭socket异常: " + e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "ConnID: " + mConnectionId + " - 心跳PING任务中发生异常: " + e.getMessage(), e);
                    mIsClientAlive.set(false);
                    try {
                        mClientSocket.close();
                    } catch (IOException ex) {
                        Log.e(TAG, "ConnID: " + mConnectionId + " PING任务异常后关闭socket异常: " + ex.getMessage());
                    }
                }
            }, HEARTBEAT_INTERVAL_MS, HEARTBEAT_INTERVAL_MS, TimeUnit.MILLISECONDS);
            Log.i(TAG, "ConnID: " + mConnectionId + " - 服务端心跳PING机制已启动。");
        }

        private void stopHeartbeatMechanism(boolean calledByThisTask) {
            if (mServicePingTask != null && !mServicePingTask.isDone()) {
                mServicePingTask.cancel(true);
            }
            if (!mHeartbeatScheduler.isShutdown()) {
                mHeartbeatScheduler.shutdownNow();
            }
            Log.i(TAG, "ConnID: " + mConnectionId + " - 服务端心跳PING机制已停止。");
        }

        private void performResourceCleanup() {
            Log.i(TAG, "ConnID: " + mConnectionId + " - 执行资源清理 for " + mClientInfo);
            stopHeartbeatMechanism(true);

            if (mPendingVDRequestForThisClient != null) {
                mPendingVDRequests.remove(mPendingVDRequestForThisClient.serviceRequestId);
                if (mPendingVDRequestForThisClient.getAssignedInternalClientId() != -1 &&
                        mPendingVDRequestForThisClient.getResultingDisplayId() != null &&
                        !mPendingVDRequestForThisClient.getResultingDisplayId().startsWith("ERROR_")) {
                    VirtualDisplayContainer vdToCheck = mActiveVirtualDisplays.get(mPendingVDRequestForThisClient.getAssignedInternalClientId());
                    if (vdToCheck != null && !vdToCheck.isManuallyCreated) { // 只释放由网络请求自动创建的VD
                        Log.i(TAG, "ConnID: " + mConnectionId + " - 清理: 释放网络VD (InternalID: " + vdToCheck.internalClientId + ")");
                        final int internalIdToRelease = vdToCheck.internalClientId;
                        // 确保在服务的主Handler上执行，避免并发问题
                        mServiceHandler.post(() -> releaseVirtualDisplay(internalIdToRelease, false, true)); // 新增参数 true 表示强制移除
                    }
                }
            }
            if (mReusedManualVDForThisClient != null) {
                Log.i(TAG, "ConnID: " + mConnectionId + " - 清理: 释放手动VD (InternalID: " + mReusedManualVDForThisClient.internalClientId + ") 的网络占用。");
                mReusedManualVDForThisClient.releaseOccupation(); // 释放占用标记
                mActiveNetworkConnections.remove(mReusedManualVDForThisClient.internalClientId);
                final int finalReusedInternalId = mReusedManualVDForThisClient.internalClientId;
                final int finalReusedDisplayId = mReusedManualVDForThisClient.displayId;
                mMainThreadHandler.post(() -> {
                    ActivityCallback currentCb;
                    synchronized (mActivityCallbackLock) {
                        currentCb = mActivityCallback;
                    }
                    if (currentCb != null) {
                        currentCb.updateManualScreenOccupationStatus(finalReusedInternalId, null, false, finalReusedDisplayId);
                    }
                });
            }
            mPendingVDRequestForThisClient = null;
            mReusedManualVDForThisClient = null;

            try {
                if (mWriter != null) mWriter.close();
            } catch (Exception e) { /* ignore */ }
            try {
                if (mReader != null) mReader.close();
            } catch (Exception e) { /* ignore */ }
            try {
                if (mClientSocket != null && !mClientSocket.isClosed()) mClientSocket.close();
            } catch (IOException e) {
                Log.e(TAG, "ConnID: " + mConnectionId + " 清理时关闭Socket出错: " + e.getMessage());
            }
            Log.i(TAG, "ConnID: " + mConnectionId + " - 资源清理完毕。");
        }

        @Override
        public void run() {
            String serviceReqIdForVD = UUID.randomUUID().toString();
            Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 已为客户端 " + mClientInfo + " 启动。");
            boolean resourcesCleaned = false;

            String userIdentifierFromClient = null;
            boolean forceCreateNew = false;
            String targetAppPackageName = null;
            String targetAppActivityName = null;

            try {
                mClientSocket.setSoTimeout(CLIENT_PONG_TIMEOUT_MS + 5000);
                mReader = new BufferedReader(new InputStreamReader(mClientSocket.getInputStream(), "UTF-8"));
                mWriter = new PrintWriter(new OutputStreamWriter(mClientSocket.getOutputStream(), "UTF-8"), true);

                mWriter.println("DSMS_CONNECTED:OK:Welcome_Smart_DSMS_v1.0_CN_HB_AppLaunch");
                String clientMessage = mReader.readLine();
                if (clientMessage == null) {
                    Log.w(TAG, "ConnID: " + mConnectionId + " - 客户端在发送数据前断开。");
                    mIsClientAlive.set(false);
                    return;
                }

                // 添加详细的调试日志
                Log.i(TAG, "=== 调试信息开始 ===");
                Log.i(TAG, "ConnID: " + mConnectionId + " - 收到原始消息: [" + clientMessage + "]");
                Log.i(TAG, "消息长度: " + clientMessage.length());
                Log.i(TAG, "消息是否以 REQUEST_VD_SMART: 开头: " + clientMessage.startsWith("REQUEST_VD_SMART:"));
                Log.i(TAG, "消息是否以 REQUEST_VD: 开头: " + clientMessage.startsWith("REQUEST_VD:"));

                // 打印每个字符的 ASCII 码，检查是否有隐藏字符
                StringBuilder asciiCodes = new StringBuilder();
                for (int i = 0; i < Math.min(clientMessage.length(), 30); i++) {
                    asciiCodes.append((int)clientMessage.charAt(i)).append(" ");
                }
                Log.i(TAG, "前30个字符的ASCII码: " + asciiCodes.toString());
                Log.i(TAG, "=== 调试信息结束 ===");

                if (clientMessage.startsWith("REQUEST_VD_SMART:")) {
                    String content = clientMessage.substring("REQUEST_VD_SMART:".length());
                    Log.d(TAG, "REQUEST_VD_SMART 内容部分: [" + content + "]");

                    String[] parts = content.split(":", 4);
                    Log.d(TAG, "分割后的部分数量: " + parts.length);
                    for (int i = 0; i < parts.length; i++) {
                        Log.d(TAG, "parts[" + i + "] = [" + parts[i] + "]");
                    }

                    userIdentifierFromClient = (parts.length > 0 && !parts[0].trim().isEmpty()) ? parts[0].trim() : "SmartUser_" + mConnectionId;
                    if (parts.length > 1) {
                        forceCreateNew = "force_new".equalsIgnoreCase(parts[1].trim());
                        Log.d(TAG, "第二个字段: [" + parts[1] + "], 是否force_new: " + forceCreateNew);
                    }
                    if (parts.length > 2 && !parts[2].trim().isEmpty())
                        targetAppPackageName = parts[2].trim();
                    if (parts.length > 3 && !parts[3].trim().isEmpty())
                        targetAppActivityName = parts[3].trim();

                    Log.i(TAG, "ConnID: " + mConnectionId + " - 解析结果: User=" + userIdentifierFromClient +
                            ", ForceNew=" + forceCreateNew + ", Pkg=" + targetAppPackageName + ", Activity=" + targetAppActivityName);
                } else if (clientMessage.startsWith("REQUEST_VD:")) {
                    userIdentifierFromClient = clientMessage.substring("REQUEST_VD:".length()).trim();
                    if (userIdentifierFromClient.isEmpty())
                        userIdentifierFromClient = "LegacyUser_" + mConnectionId;
                    forceCreateNew = false;
                    targetAppPackageName = null;
                    targetAppActivityName = null;
                    Log.d(TAG, "ConnID: " + mConnectionId + " - Parsed Legacy Request: User=" + userIdentifierFromClient);
                } else if ("PY_HEARTBEAT_PING".equals(clientMessage.trim())) {
                    // 处理客户端在请求阶段发送的心跳PING
                    Log.d(TAG, "ConnID: " + mConnectionId + " - 在请求阶段收到客户端 PING, 回复 PONG。");
                    if (mWriter != null && !mWriter.checkError()) {
                        mWriter.println("DSMS_HEARTBEAT_PONG");
                    }
                    // 继续读取真正的请求消息
                    String actualRequest = mReader.readLine();
                    if (actualRequest == null) {
                        Log.w(TAG, "ConnID: " + mConnectionId + " - 客户端在发送PING后断开。");
                        mIsClientAlive.set(false);
                        return;
                    }

                    Log.i(TAG, "ConnID: " + mConnectionId + " - 在PING后收到实际请求: [" + actualRequest + "]");

                    // 重新解析实际请求
                    if (actualRequest.startsWith("REQUEST_VD_SMART:")) {
                        String content = actualRequest.substring("REQUEST_VD_SMART:".length());
                        Log.d(TAG, "REQUEST_VD_SMART 内容部分: [" + content + "]");

                        String[] parts = content.split(":", 4);
                        Log.d(TAG, "分割后的部分数量: " + parts.length);
                        for (int i = 0; i < parts.length; i++) {
                            Log.d(TAG, "parts[" + i + "] = [" + parts[i] + "]");
                        }

                        userIdentifierFromClient = (parts.length > 0 && !parts[0].trim().isEmpty()) ? parts[0].trim() : "SmartUser_" + mConnectionId;
                        if (parts.length > 1) {
                            forceCreateNew = "force_new".equalsIgnoreCase(parts[1].trim());
                            Log.d(TAG, "第二个字段: [" + parts[1] + "], 是否force_new: " + forceCreateNew);
                        }
                        if (parts.length > 2 && !parts[2].trim().isEmpty())
                            targetAppPackageName = parts[2].trim();
                        if (parts.length > 3 && !parts[3].trim().isEmpty())
                            targetAppActivityName = parts[3].trim();

                        Log.i(TAG, "ConnID: " + mConnectionId + " - 解析结果: User=" + userIdentifierFromClient +
                                ", ForceNew=" + forceCreateNew + ", Pkg=" + targetAppPackageName + ", Activity=" + targetAppActivityName);
                    } else if (actualRequest.startsWith("REQUEST_VD:")) {
                        userIdentifierFromClient = actualRequest.substring("REQUEST_VD:".length()).trim();
                        if (userIdentifierFromClient.isEmpty())
                            userIdentifierFromClient = "LegacyUser_" + mConnectionId;
                        forceCreateNew = false;
                        targetAppPackageName = null;
                        targetAppActivityName = null;
                        Log.d(TAG, "ConnID: " + mConnectionId + " - Parsed Legacy Request: User=" + userIdentifierFromClient);
                    } else {
                        Log.w(TAG, "ConnID: " + mConnectionId + " 在PING后收到无效请求: [" + actualRequest + "]");
                        mWriter.println("ERROR:NA:无效请求格式。");
                        mIsClientAlive.set(false);
                        return;
                    }
                } else {
                    Log.w(TAG, "ConnID: " + mConnectionId + " 无效请求，既不是 REQUEST_VD_SMART: 也不是 REQUEST_VD:");
                    Log.w(TAG, "实际消息: [" + clientMessage + "]");
                    mWriter.println("ERROR:NA:无效请求格式。");
                    mIsClientAlive.set(false);
                    return;
                }

                mPendingVDRequestForThisClient = new PendingVDRequest(serviceReqIdForVD, userIdentifierFromClient, mClientSocket, mWriter, forceCreateNew);
                mPendingVDRequests.put(serviceReqIdForVD, mPendingVDRequestForThisClient);

                handleVirtualDisplayRequest(mPendingVDRequestForThisClient);

                if (!mIsClientAlive.get() || mPendingVDRequestForThisClient == null ||
                        mPendingVDRequestForThisClient.getResultingDisplayId() == null ||
                        mPendingVDRequestForThisClient.getResultingDisplayId().startsWith("ERROR_")) {
                    Log.i(TAG, "ConnID: " + mConnectionId + " -未能成功分配Display或在分配前连接已失效。");
                    mIsClientAlive.set(false);
                } else {
                    String obtainedDisplayIdStr = mPendingVDRequestForThisClient.getResultingDisplayId();
                    int obtainedDisplayId = -1;
                    try {
                        obtainedDisplayId = Integer.parseInt(obtainedDisplayIdStr);
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "ConnID: " + mConnectionId + " - 无效的Display ID格式: " + obtainedDisplayIdStr);
                        mIsClientAlive.set(false);
                        if (mWriter != null && !mWriter.checkError()) { // 确保writer可用
                            mWriter.println("ERROR:" + serviceReqIdForVD + ":内部DisplayID格式错误");
                        }
                    }

                    if (mIsClientAlive.get() && obtainedDisplayId != -1) {
                        if (targetAppPackageName != null && !targetAppPackageName.isEmpty()) {
                            Log.i(TAG, "ConnID: " + mConnectionId + " - Display " + obtainedDisplayId + " 已分配, 准备启动应用: " + targetAppPackageName);
                            launchAppOnDisplay(targetAppPackageName, targetAppActivityName, obtainedDisplayId, serviceReqIdForVD);
                        } else {
                            Log.i(TAG, "ConnID: " + mConnectionId + " - Display " + obtainedDisplayId + " 已分配, 客户端未请求启动应用。");
                            if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                                mWriter.println("APP_LAUNCH_STATUS:" + serviceReqIdForVD + ":OK:NoAppRequested");
                            }
                        }

                        if (mIsClientAlive.get()) {
                            startHeartbeatMechanism();
                            Log.d(TAG, "ConnID: " + mConnectionId + " - 所有初始操作完成，进入主消息监听循环...");

                            String line;
                            while (mIsClientAlive.get() && (line = mReader.readLine()) != null) {
                                if ("PY_HEARTBEAT_PONG".equals(line.trim())) {
                                    mLastClientPongTime.set(System.currentTimeMillis());
                                } else if ("PY_HEARTBEAT_PING".equals(line.trim())) {
                                    Log.d(TAG, "ConnID: " + mConnectionId + " - 收到客户端 PING, 回复 PONG。");
                                    if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                                        mWriter.println("DSMS_HEARTBEAT_PONG");
                                    }
                                    if (mWriter.checkError()) {
                                        Log.e(TAG, "ConnID: " + mConnectionId + " - 回复客户端 PONG 时IO错误。");
                                        mIsClientAlive.set(false);
                                    }
                                } else {
                                    Log.w(TAG, "ConnID: " + mConnectionId + " - 收到未知客户端消息: " + line);
                                }
                            }
                            if (mIsClientAlive.get()) {
                                Log.i(TAG, "ConnID: " + mConnectionId + " - 客户端 " + mClientInfo + " 已正常断开 (readLine返回null)。");
                                mIsClientAlive.set(false);
                            }
                        }
                    }
                }
            } catch (java.net.SocketTimeoutException ste) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - Socket read 超时: " + ste.getMessage());
                mIsClientAlive.set(false);
            } catch (SocketException se) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - SocketException (客户端 " + mClientInfo + "): " + se.getMessage());
                mIsClientAlive.set(false);
            } catch (IOException ioe) {
                Log.e(TAG, "ConnID: " + mConnectionId + " - IOException (客户端 " + mClientInfo + "): " + ioe.getMessage(), ioe);
                mIsClientAlive.set(false);
            } catch (InterruptedException ie) {
                Log.w(TAG, "ConnID: " + mConnectionId + " - Handler被中断: " + ie.getMessage());
                Thread.currentThread().interrupt();
                mIsClientAlive.set(false);
            } catch (Exception e) {
                Log.e(TAG, "ConnID: " + mConnectionId + " - Handler发生未预期异常: " + e.getMessage(), e);
                mIsClientAlive.set(false);
            } finally {
                Log.d(TAG, "ConnID: " + mConnectionId + " - Handler进入finally块。mIsClientAlive=" + mIsClientAlive.get() + ", resourcesCleaned=" + resourcesCleaned);
                if (mIsClientAlive.get() == false && !resourcesCleaned) { // 如果客户端已经标记为非活动状态且资源未清理
                    performResourceCleanup();
                    resourcesCleaned = true;
                } else if (!resourcesCleaned) { // 确保在任何退出路径上都清理资源
                    mIsClientAlive.set(false); // 确保标记为非活动
                    performResourceCleanup();
                }
                Log.i(TAG, "ClientSocketHandler (ConnID: " + mConnectionId + ") 已为客户端 " + mClientInfo + " 执行完毕。");
            }
        }

        private void handleVirtualDisplayRequest(PendingVDRequest request) throws InterruptedException {
            final String userIdentifier = request.userIdentifier;
            final boolean forceCreateNew = request.forceCreateNew;
            final String serviceReqId = request.serviceRequestId;

            if (!mIsClientAlive.get()) {
                request.setResult(-1, "ERROR_CONNECTION_LOST_BEFORE_PROCESSING");
                return;
            }

            ActivityCallback initialCallback;
            synchronized (mActivityCallbackLock) {
                initialCallback = mActivityCallback;
            }
            if (initialCallback == null) {
                Log.e(TAG, "ConnID: " + mConnectionId + " VD请求 (ReqID: " + serviceReqId + "): ActivityCallback为null。");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("ERROR:" + serviceReqId + ":DSMS Activity不可用");
                request.setResult(-1, "ERROR_ACTIVITY_CALLBACK_NULL");
                mIsClientAlive.set(false);
                return;
            }

            // 尝试复用逻辑
            if (!forceCreateNew) {
                mReusedManualVDForThisClient = findAndOccupyReusableManualVD(userIdentifier);
                if (mReusedManualVDForThisClient != null) {
                    if (!mIsClientAlive.get()) { // 再次检查连接状态
                        mReusedManualVDForThisClient.releaseOccupation(); // 如果连接在决策后丢失，释放占用
                        request.setResult(-1, "ERROR_CONNECTION_LOST_AFTER_REUSE_DECISION");
                        return;
                    }
                    mActiveNetworkConnections.put(mReusedManualVDForThisClient.internalClientId, new NetworkClientConnection(userIdentifier, mConnectionId));
                    Log.d(TAG, "ConnID: " + mConnectionId + " 已为复用VD InternalID " + mReusedManualVDForThisClient.internalClientId + " 添加网络连接记录");
                    if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                        mWriter.println("VD_REUSED:" + mReusedManualVDForThisClient.internalClientId + ":" + mReusedManualVDForThisClient.displayId);
                    final int reusedId = mReusedManualVDForThisClient.internalClientId;
                    final int reusedDispId = mReusedManualVDForThisClient.displayId;
                    mMainThreadHandler.post(() -> {
                        ActivityCallback cb;
                        synchronized (mActivityCallbackLock) {
                            cb = mActivityCallback;
                        }
                        if (cb != null)
                            cb.updateManualScreenOccupationStatus(reusedId, "网络:" + userIdentifier, true, reusedDispId);
                    });
                    request.setResult(reusedId, String.valueOf(reusedDispId));
                    return;
                } else {
                    Log.i(TAG, "ConnID: " + mConnectionId + " - 未找到可复用的手动VD，将尝试创建新的。");
                }
            }

            // 创建新VD的逻辑 (如果forceCreateNew为true，或未找到可复用的)
            if (!mIsClientAlive.get()) {
                request.setResult(-1, "ERROR_CONNECTION_LOST_BEFORE_NEW_VD_PROCESS");
                return;
            }
            Log.i(TAG, "ConnID: " + mConnectionId + " " + (forceCreateNew ? "强制" : "自动") + "创建新VD给用户: " + userIdentifier);
            if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                mWriter.println("VD_PENDING:" + serviceReqId + ":请求已接收，处理VD创建...");

            final int assignedInternalId = mInternalClientIdCounter.getAndIncrement();
            request.assignedInternalClientId = assignedInternalId; // 确保在请求对象中设置ID

            ActivityCallback currentCallbackForNewVD;
            synchronized (mActivityCallbackLock) {
                currentCallbackForNewVD = mActivityCallback;
            }
            if (currentCallbackForNewVD == null) {
                Log.e(TAG, "ConnID: " + mConnectionId + " VD请求 (ReqID: " + serviceReqId + ") 创建新VD时ActivityCallback为null。");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("ERROR:" + serviceReqId + ":DSMS Activity在处理中不可用。");
                request.setResult(assignedInternalId, "ERROR_ACTIVITY_CALLBACK_NULL_FOR_NEW_VD");
                mIsClientAlive.set(false);
                return;
            }

            final String finalServiceReqIdForLambda = serviceReqId;
            final String finalUserIdentifierForLambda = userIdentifier;
            final int finalAssignedInternalIdForLambda = assignedInternalId;
            if (!currentCallbackForNewVD.isMediaProjectionReady()) {
                Log.i(TAG, "ConnID: " + mConnectionId + " 新VD (ReqID: " + serviceReqId + "): MP未就绪。请求授权。");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("VD_PENDING:" + serviceReqId + ":等待用户授予MP权限...");
                final ActivityCallback finalCbForMP = currentCallbackForNewVD;
                mMainThreadHandler.post(() -> finalCbForMP.requestMediaProjectionFromUser(finalServiceReqIdForLambda));
            } else {
                Log.i(TAG, "ConnID: " + mConnectionId + " 新VD (ReqID: " + serviceReqId + "): MP已就绪。请求Activity创建UI。");
                final ActivityCallback finalCbForUI = currentCallbackForNewVD;
                // 注意：这里传递的是新生成的 assignedInternalId
                mMainThreadHandler.post(() -> finalCbForUI.onRequestNewVirtualDisplayUI(finalServiceReqIdForLambda, finalUserIdentifierForLambda, finalAssignedInternalIdForLambda));
            }

            Log.d(TAG, "ConnID: " + mConnectionId + " 等待VD创建完成的completionLatch (ReqID: " + serviceReqId + ")");
            if (!request.completionLatch.await(120, TimeUnit.SECONDS)) {
                Log.e(TAG, "ConnID: " + mConnectionId + " 新VD请求 (ReqID: " + serviceReqId + ") 等待VD创建或MP权限超时。");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("ERROR:" + serviceReqId + ":等待DisplayID或权限超时。");
                // 即使超时，也用之前分配的ID来标记这个失败的请求
                request.setResult(request.getAssignedInternalClientId() != -1 ? request.getAssignedInternalClientId() : assignedInternalId, "ERROR_TIMEOUT_WAITING_FOR_DISPLAY_OR_PERMISSION");
                mIsClientAlive.set(false);
                return;
            }

            if (!mIsClientAlive.get()) {
                request.setResult(request.getAssignedInternalClientId(), "ERROR_CONNECTION_LOST_DURING_VD_CREATION");
                return;
            }

            if (request.wasMediaProjectionDenied()) {
                Log.w(TAG, "ConnID: " + mConnectionId + " 新VD请求 (ReqID: " + serviceReqId + ") 失败: MP被用户拒绝。");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("ERROR:" + serviceReqId + ":MediaProjection权限被拒绝。");
                // 使用已分配的ID标记这个失败
                // request.setResult(...) 已经在 request.setMediaProjectionDenied() 中通过 latch.countDown() 触发了，这里不需要再次调用
            } else if (request.getResultingDisplayId() != null && !request.getResultingDisplayId().startsWith("ERROR_")) {
                mActiveNetworkConnections.put(request.getAssignedInternalClientId(), new NetworkClientConnection(userIdentifier, mConnectionId));
                Log.d(TAG, "ConnID: " + mConnectionId + " 已为新VD InternalID " + request.getAssignedInternalClientId() + " 添加网络连接记录");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("VD_CREATED:" + request.getAssignedInternalClientId() + ":" + request.getResultingDisplayId());
                final int createdId = request.getAssignedInternalClientId();
                final int createdDispId;
                try {
                    createdDispId = Integer.parseInt(request.getResultingDisplayId());
                    mMainThreadHandler.post(() -> {
                        ActivityCallback cb;
                        synchronized (mActivityCallbackLock) {
                            cb = mActivityCallback;
                        }
                        if (cb != null)
                            cb.updateManualScreenOccupationStatus(createdId, "网络:" + userIdentifier, true, createdDispId);
                    });
                } catch (NumberFormatException e) {
                    Log.e(TAG, "ConnID: " + mConnectionId + " - 解析新创建的DisplayID失败: " + request.getResultingDisplayId());
                    // 此时Display已创建，但无法更新UI状态，这算是一个内部错误
                }
            } else {
                String errMsg = request.getResultingDisplayId() != null ? request.getResultingDisplayId() : "未知内部错误导致VD创建失败。";
                Log.e(TAG, "ConnID: " + mConnectionId + " 新VD请求 (ReqID: " + serviceReqId + ") 处理出错: " + errMsg);
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError())
                    mWriter.println("ERROR:" + serviceReqId + ":" + errMsg);
            }
        }

        private void launchAppOnDisplay(String packageName, @Nullable String activityName, int displayId, String reqId) {
            Log.i(TAG, "launchAppOnDisplay CALLED. ConnID: " + mConnectionId +
                    " - Pkg: " + packageName +
                    ", Activity: " + activityName +
                    ", DisplayID: " + displayId +
                    ", ReqID: " + reqId);

            if (packageName == null || packageName.isEmpty()) {
                Log.e(TAG, "launchAppOnDisplay: 包名为空，无法启动。");
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                    mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":ERROR:PackageNameMissing");
                }
                return;
            }

            try {
                // 获取 PackageManager 来查找应用信息
                PackageManager pm = getPackageManager();
                Intent launchIntent = null;

                if (activityName != null && !activityName.isEmpty()) {
                    // 如果指定了 Activity，创建明确的 Intent
                    ComponentName componentName;
                    if (activityName.startsWith(".")) {
                        // 相对类名
                        componentName = new ComponentName(packageName, packageName + activityName);
                    } else if (activityName.contains(".")) {
                        // 完整类名
                        componentName = new ComponentName(packageName, activityName);
                    } else {
                        // 简单类名，尝试添加点前缀
                        componentName = new ComponentName(packageName, packageName + "." + activityName);
                    }

                    launchIntent = new Intent();
                    launchIntent.setComponent(componentName);
                } else {
                    // 如果没有指定 Activity，获取默认启动 Intent
                    launchIntent = pm.getLaunchIntentForPackage(packageName);
                    if (launchIntent == null) {
                        throw new Exception("无法获取包 " + packageName + " 的启动 Intent");
                    }
                }

                // 设置必要的标志
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);

                // 创建 ActivityOptions 来指定目标显示器
                ActivityOptions options;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    options = ActivityOptions.makeBasic();
                } else {
                    options = null;
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    options.setLaunchDisplayId(displayId);
                }

                // 在主线程上启动 Activity
                Intent finalLaunchIntent = launchIntent;
                mMainThreadHandler.post(() -> {
                    try {
                        Context context = VirtualDisplayManagementService.this;
                        context.startActivity(finalLaunchIntent, options.toBundle());

                        Log.i(TAG, "launchAppOnDisplay: 成功发送启动 Intent 到 Display " + displayId);
                        // 将网络操作移到后台线程
                        mServiceHandler.post(() -> {
                            if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                                mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":OK:IntentSent");
                            }
                        });
                    } catch (ActivityNotFoundException e) {
                        Log.e(TAG, "launchAppOnDisplay: Activity 未找到 - " + e.getMessage());
                        // 将网络操作移到后台线程
                        mServiceHandler.post(() -> {
                            if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                                mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":ERROR:ActivityNotFound_" +
                                        sanitizeErrorMessage(e.getMessage()));
                            }
                        });
                    } catch (SecurityException e) {
                        Log.e(TAG, "launchAppOnDisplay: 安全异常 - " + e.getMessage());
                        // 将网络操作移到后台线程
                        mServiceHandler.post(() -> {
                            if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                                mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":ERROR:SecurityException_" +
                                        sanitizeErrorMessage(e.getMessage()));
                            }
                        });
                    } catch (Exception e) {
                        Log.e(TAG, "launchAppOnDisplay: 意外错误 - " + e.getMessage(), e);
                        // 将网络操作移到后台线程
                        mServiceHandler.post(() -> {
                            if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                                mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":ERROR:UnexpectedException_" +
                                        sanitizeErrorMessage(e.getMessage()));
                            }
                        });
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "launchAppOnDisplay: 准备启动时出错 - " + e.getMessage(), e);
                if (mIsClientAlive.get() && mWriter != null && !mWriter.checkError()) {
                    mWriter.println("APP_LAUNCH_STATUS:" + reqId + ":ERROR:PreparationError_" +
                            sanitizeErrorMessage(e.getMessage()));
                }
            }
        }

        private String sanitizeErrorMessage(String message) {
            if (message == null) return "UnknownError";
            String sanitized = message.replaceAll("[:\n\r\\s]+", "_"); // 替换冒号、换行、回车和空白字符为下划线
            return sanitized.substring(0, Math.min(sanitized.length(), 80));
        }
    } // --- End of SmartClientSocketHandler ---


    // --- 其他 Service 方法 (findAndOccupyReusableManualVD, getDisplayNetworkUserInfo, notifyVirtualDisplayProcessed, 等) ---
    private synchronized @Nullable VirtualDisplayContainer findAndOccupyReusableManualVD(String networkUserIdentifier) {
        Log.d(TAG, "正在为网络用户 '" + networkUserIdentifier + "' 查找可复用的手动VD (最早优先)...");
        List<VirtualDisplayContainer> availableManualVDs = new ArrayList<>();
        for (VirtualDisplayContainer vdContainer : mActiveVirtualDisplays.values()) {
            if (vdContainer.isManuallyCreated && vdContainer.virtualDisplay != null && vdContainer.virtualDisplay.getDisplay() != null && !vdContainer.isCurrentlyOccupiedByNetwork()) {
                availableManualVDs.add(vdContainer);
            }
        }

        if (availableManualVDs.isEmpty()) {
            Log.i(TAG, "扫描完成：没有找到可供用户 '" + networkUserIdentifier + "' 复用的、空闲且有效的手动VD。");
            return null;
        }

        // 按创建时间戳升序排序
        Collections.sort(availableManualVDs, new Comparator<VirtualDisplayContainer>() {
            @Override
            public int compare(VirtualDisplayContainer o1, VirtualDisplayContainer o2) {
                return Long.compare(o1.getCreationTime(), o2.getCreationTime());
            }
        });

        VirtualDisplayContainer earliestAvailable = availableManualVDs.get(0); // 获取最早创建的那个
        if (earliestAvailable.tryOccupy(networkUserIdentifier)) {
            Log.i(TAG, "找到并成功占用最早的手动VD: InternalID=" + earliestAvailable.internalClientId + " (创建于 " + earliestAvailable.getCreationTime() + ") 给用户 '" + networkUserIdentifier + "'");
            return earliestAvailable;
        } else {
            Log.w(TAG, "找到最早手动VD (InternalID: " + earliestAvailable.internalClientId + ") 但占用失败（可能并发导致）。将尝试下一个（如果适用）或返回null。");
            // 理论上 tryOccupy 失败的概率很低，因为我们是在同步块中操作 mActiveVirtualDisplays (间接通过 findAndOccupyReusableManualVD 的 synchronized)
            // 并且 isCurrentlyOccupiedByNetwork 应该已经过滤掉了。但为了健壮性，记录日志。
            // 如果确实需要处理并发占用失败，这里可以迭代列表中的下一个。但当前设计下，第一个可用的就应该是能被占用的。
            return null; // 或者重新尝试查找，但简单起见，先返回null
        }
    }

    public boolean isDisplayBeingUsedByNetworkClient(int internalClientId) {
        return mActiveNetworkConnections.containsKey(internalClientId);
    }

    public @Nullable String getDisplayNetworkUserInfo(int internalClientId) {
        NetworkClientConnection connection = mActiveNetworkConnections.get(internalClientId);
        if (connection != null) {
            long connectedDurationMs = System.currentTimeMillis() - connection.connectionTime;
            String connIdPart = "N/A";
            if (connection.connectionId != null && !connection.connectionId.isEmpty()) {
                connIdPart = connection.connectionId.length() >= 4 ? connection.connectionId.substring(0, 4) : connection.connectionId;
            }
            return connection.userIdentifier + " (ConnID: " + connIdPart + ", " + (connectedDurationMs / 1000) + "s)";
        }
        return null;
    }

    public void notifyVirtualDisplayProcessed(String serviceRequestId, int internalClientId, @Nullable String displayIdOrError) {
        PendingVDRequest request = mPendingVDRequests.get(serviceRequestId);
        if (request != null) {
            Log.i(TAG, "服务接收到VD处理完成通知: ReqID=" + serviceRequestId + ", InternalID=" + internalClientId + ", 结果=" + displayIdOrError);
            request.setResult(internalClientId, displayIdOrError);
        } else {
            Log.w(TAG, "服务接收到VD处理通知，但未找到对应的挂起请求ID: " + serviceRequestId);
        }
    }

    public void notifyMediaProjectionDenied(String serviceRequestId) {
        PendingVDRequest request = mPendingVDRequests.get(serviceRequestId);
        if (request != null) {
            Log.w(TAG, "服务接收到MediaProjection被拒绝通知: ReqID=" + serviceRequestId);
            request.setMediaProjectionDenied();
        } else {
            Log.w(TAG, "服务接收到MP被拒绝通知，但未找到对应的挂起请求ID: " + serviceRequestId);
        }
    }

    public void retryRequestAfterMediaProjection(String serviceRequestId, String userIdentifier, int assignedInternalClientIdFromActivity) {
        PendingVDRequest request = mPendingVDRequests.get(serviceRequestId);
        if (request != null) {
            Log.i(TAG, "MP权限已获取，尝试重新处理请求: " + serviceRequestId);
            ActivityCallback currentCb;
            synchronized (mActivityCallbackLock) {
                currentCb = mActivityCallback;
            }
            if (currentCb != null) {
                int idToUseInUiRequest = assignedInternalClientIdFromActivity;
                // 如果Activity没有预设ID (-1), 且request中已有分配的ID (例如之前尝试创建但MP未就绪)，则沿用request中的ID
                if (idToUseInUiRequest == -1 && request.getAssignedInternalClientId() != -1) {
                    idToUseInUiRequest = request.getAssignedInternalClientId();
                } else if (idToUseInUiRequest == -1) { // Activity未预设，request中也无，则新生成
                    idToUseInUiRequest = mInternalClientIdCounter.getAndIncrement();
                }
                // 无论如何，同步request中的ID
                request.assignedInternalClientId = idToUseInUiRequest;

                final int finalIdToUseInUiRequest = idToUseInUiRequest;
                final String finalServiceRequestId = serviceRequestId;
                final String finalUserIdentifier = userIdentifier; // 使用从Activity回调过来的userIdentifier，或者request中的
                final ActivityCallback finalCb = currentCb;
                mMainThreadHandler.post(() -> finalCb.onRequestNewVirtualDisplayUI(finalServiceRequestId, finalUserIdentifier, finalIdToUseInUiRequest));
            } else {
                Log.e(TAG, "MP就绪后重试请求 " + serviceRequestId + " 失败，ActivityCallback为null。");
                request.setResult(request.getAssignedInternalClientId(), "ERROR_CALLBACK_NULL_ON_MP_RETRY");
            }
        } else {
            Log.w(TAG, "MP就绪后尝试重试请求，但未找到挂起请求: " + serviceRequestId);
        }
    }

    public void notifyMediaProjectionRequestBusy(String newRequestId, String existingRequestId) {
        PendingVDRequest request = mPendingVDRequests.get(newRequestId);
        if (request != null) {
            Log.w(TAG, "MP请求 " + newRequestId + " 失败，因为已有请求 " + existingRequestId + " 正在等待MP权限。");
            if (request.writer != null && !request.writer.checkError()) {
                request.writer.println("ERROR:" + newRequestId + ":另一个屏幕权限请求正在处理中，请稍后。");
            }
            request.setResult(request.getAssignedInternalClientId(), "ERROR_MP_REQUEST_BUSY");
        }
    }

    public @Nullable VirtualDisplayContainer createVirtualDisplayForClientWithProjection(
            int internalClientId, @NonNull Surface targetSurface,
            @NonNull MediaProjection mediaProjection, @NonNull String initialUserIdentifier, boolean isManual) {
        if (mDisplayManager == null || !targetSurface.isValid()) {
            Log.e(TAG, "创建VD的前提条件未满足。InternalID=" + internalClientId);
            return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "DisplayManager或Surface无效");
        }
        String sanitizedId = initialUserIdentifier.replaceAll("[^a-zA-Z0-9_-]", "_");
        String vdName = (isManual ? "ManualVD_" : "NetVD_") + sanitizedId + "_" + internalClientId + "_" + (System.currentTimeMillis() % 10000);
        DisplayMetrics metrics = getResources().getDisplayMetrics();
        // 使用稍微小一点的尺寸，或者与设备主屏相关的比例
        int width = Math.max(1024, metrics.widthPixels / 2);
        int height = Math.max(600, (width * 9) / 16); // 保持16:9的示例高度
        int densityDpi = metrics.densityDpi;
        int vdFlags = DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION | DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC;
        // 注意：VIRTUAL_DISPLAY_FLAG_SHOULD_SHOW_SYSTEM_DECORATIONS (512) 需要 INTERNAL_SYSTEM_WINDOW 权限
        // 普通应用无法获得此权限，因此不添加此标志
        Log.d(TAG, "使用基本VD标志: PRESENTATION | PUBLIC");
        VirtualDisplay virtualDisplayInstance = null;
        try {
            Log.i(TAG, "正在尝试创建VD: " + vdName + " (手动=" + isManual + ", 大小=" + width + "x" + height + ", DPI=" + densityDpi + ", Flags=" + vdFlags + ")");
            virtualDisplayInstance = mediaProjection.createVirtualDisplay(vdName, width, height, densityDpi, vdFlags, targetSurface, null, null);
        } catch (SecurityException se) {
            Log.e(TAG, "创建VD'" + vdName + "' 时发生SecurityException: " + se.getMessage(), se);
            return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "MediaProjection安全异常: " + se.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "创建VD'" + vdName + "' 时发生异常: " + e.getMessage(), e);
            return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "创建VD时异常: " + e.getMessage());
        }

        if (virtualDisplayInstance != null && virtualDisplayInstance.getDisplay() != null) {
            VirtualDisplayContainer container = new VirtualDisplayContainer(virtualDisplayInstance, targetSurface, internalClientId, initialUserIdentifier, isManual);
            if (container.displayId == -1) {
                Log.e(TAG, "VD '" + vdName + "' 已创建，但DisplayID无效。释放此VD。");
                if (virtualDisplayInstance != null) virtualDisplayInstance.release();
                return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "获取的DisplayID无效");
            }
            mActiveVirtualDisplays.put(internalClientId, container);
            Log.i(TAG, "VD创建成功: " + vdName + " (DisplayID: " + container.displayId + ")");
            return container;
        } else {
            Log.e(TAG, "创建VD '" + vdName + "' 失败或未能获取Display对象。");
            if (virtualDisplayInstance != null) virtualDisplayInstance.release();
            return new VirtualDisplayContainer(internalClientId, initialUserIdentifier, isManual, "创建VD失败或Display对象为空");
        }
    }

    /**
     * 释放虚拟显示器。
     *
     * @param internalClientId           内部客户端ID
     * @param calledFromSurfaceDestroyed 是否由Surface销毁回调触发
     * @param forceRemoveNetVD           如果是非手动创建的VD (NetVD)，是否强制从 activeVirtualDisplays 移除（例如客户端断开时）
     */
    public void releaseVirtualDisplay(int internalClientId, boolean calledFromSurfaceDestroyed, boolean forceRemoveNetVD) {
        final int finalInternalClientId = internalClientId;
        final boolean finalCalledFromSurfaceDestroyed = calledFromSurfaceDestroyed;
        mServiceHandler.post(() -> {
            VirtualDisplayContainer container = mActiveVirtualDisplays.get(finalInternalClientId); // 先get，不直接remove
            if (container == null) {
                Log.w(TAG, "尝试释放VD (InternalID: " + finalInternalClientId + "), 但未在活动列表中找到。");
                return;
            }
            Log.i(TAG, "请求释放VD (InternalID: " + finalInternalClientId + "), 用户='" + container.initialUserIdentifier +
                    "', 手动=" + container.isManuallyCreated + ", SurfaceDestroy调用=" + finalCalledFromSurfaceDestroyed +
                    ", 强制移除NetVD=" + forceRemoveNetVD);

            NetworkClientConnection removedConnection = mActiveNetworkConnections.remove(finalInternalClientId);
            if (removedConnection != null) {
                Log.i(TAG, "已清理VD " + finalInternalClientId + " 的网络连接记录，用户: " + removedConnection.userIdentifier);
            }

            // 对于手动创建的VD，如果不是Surface销毁导致的释放，我们通常只释放其网络占用，保留VD本身
            // 除非是Service销毁等特殊情况
            if (container.isManuallyCreated && !finalCalledFromSurfaceDestroyed) {
                if (container.isCurrentlyOccupiedByNetwork()) {
                    container.releaseOccupation();
                    final int finalDisplayIdForCallback = container.displayId;
                    mMainThreadHandler.post(() -> {
                        ActivityCallback currentCb;
                        synchronized (mActivityCallbackLock) {
                            currentCb = mActivityCallback;
                        }
                        if (currentCb != null) {
                            currentCb.updateManualScreenOccupationStatus(finalInternalClientId, null, false, finalDisplayIdForCallback);
                        }
                    });
                    Log.i(TAG, "手动VD " + finalInternalClientId + " 释放了网络占用，屏幕保留在活动列表。");
                } else {
                    Log.d(TAG, "手动VD " + finalInternalClientId + " 未被网络占用或非Surface销毁，不执行VD释放。");
                }
                // 不从 mActiveVirtualDisplays 移除手动创建的VD，除非是SurfaceDestroyed或服务销毁
            } else { // 非手动VD，或者手动VD但由Surface销毁触发，或者服务销毁时
                VirtualDisplayContainer removedContainer = mActiveVirtualDisplays.remove(finalInternalClientId);
                if (removedContainer == null) { // 可能在并发情况下已经被移除
                    Log.w(TAG, "尝试移除VD " + finalInternalClientId + " 时发现已被其他操作移除。");
                    return;
                }

                if (removedContainer.virtualDisplay != null) {
                    try {
                        removedContainer.virtualDisplay.release();
                        Log.d(TAG, "VD (InternalID: " + finalInternalClientId + ", DisplayID: " + removedContainer.displayId + ") 的VD对象已释放。");
                    } catch (Exception e) {
                        Log.e(TAG, "释放VD " + finalInternalClientId + " 的VD对象时异常: " + e.getMessage());
                    }
                }
                Log.i(TAG, "VD " + finalInternalClientId + " 已从活动列表彻底移除。");

                // 如果是手动创建的VD被彻底移除了 (例如SurfaceDestroyed)，也需要更新UI
                if (removedContainer.isManuallyCreated) {
                    final int finalDisplayIdForCallback = removedContainer.displayId;
                    mMainThreadHandler.post(() -> {
                        ActivityCallback currentCb;
                        synchronized (mActivityCallbackLock) {
                            currentCb = mActivityCallback;
                        }
                        if (currentCb != null) {
                            currentCb.updateManualScreenOccupationStatus(finalInternalClientId, null, false, finalDisplayIdForCallback);
                        } // 标记为未占用
                    });
                }
            }
        });
    }
    // --- End of other Service methods ---

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        synchronized (mActivityCallbackLock) {
            mActivityCallback = null;
        }
        Log.d(TAG, "Activity解绑，回调已清除。");
        return super.onUnbind(intent);
    }

    @Override
    public void onDestroy() {
        Log.i(TAG, "DSMS服务正在销毁...");
        closeServerSocket();
        if (mClientHandlingExecutor != null && !mClientHandlingExecutor.isShutdown()) {
            mClientHandlingExecutor.shutdownNow();
            try {
                if (!mClientHandlingExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    Log.w(TAG, "客户端处理线程池在5秒内未能终止。");
                }
            } catch (InterruptedException e) {
                mClientHandlingExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        Log.i(TAG, "正在释放所有剩余的虚拟显示器...");
        // 创建一个当前键的副本进行迭代，以避免 ConcurrentModificationException
        List<Integer> idsToRelease = new ArrayList<>(mActiveVirtualDisplays.keySet());
        for (Integer id : idsToRelease) {
            releaseVirtualDisplay(id, true, true); // onDestroy时，所有VD都应被彻底移除
        }
        // 等待一小段时间确保释放操作在handler线程上执行完毕
        try {
            if (mServiceHandler != null) {
                final CountDownLatch latch = new CountDownLatch(1);
                mServiceHandler.post(latch::countDown); // 发送一个空任务到队列末尾
                if (!latch.await(2, TimeUnit.SECONDS)) { // 等待这个任务执行
                    Log.w(TAG, "等待VD释放操作完成超时。");
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            Log.w(TAG, "等待VD释放时被中断。");
        }

        mActiveVirtualDisplays.clear();
        mActiveNetworkConnections.clear();
        mPendingVDRequests.clear();
        if (mServiceHandlerThread != null) {
            mServiceHandlerThread.quitSafely();
            try {
                mServiceHandlerThread.join(1000);
            } catch (InterruptedException e) {
                Log.w(TAG, "服务后台线程join被中断");
            }
        }
        stopForeground(true);
        Log.i(TAG, "DSMS服务已完全销毁。");
        super.onDestroy();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel sc = new NotificationChannel(NOTIFICATION_CHANNEL_ID, "DSMS前台服务", NotificationManager.IMPORTANCE_LOW);
            getSystemService(NotificationManager.class).createNotificationChannel(sc);
        }
    }

    private Notification createServiceNotification() {
        Intent ni = new Intent(this, MainActivity.class);
        ni.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        int pif = PendingIntent.FLAG_UPDATE_CURRENT | (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? PendingIntent.FLAG_IMMUTABLE : 0);
        PendingIntent pi = PendingIntent.getActivity(this, 0, ni, pif);
        return new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID).setContentTitle("DSMS活动中").setContentText("正在管理虚拟显示器").setSmallIcon(R.mipmap.ic_launcher).setContentIntent(pi).setOngoing(true).build();
    }
}
