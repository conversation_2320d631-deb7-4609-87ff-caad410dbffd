<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:layout_marginBottom="8dp"
    android:background="#DDDDDD">

    <TextView
        android:id="@+id/tvClientId"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Client ID: Pending"
        android:textAppearance="?android:attr/textAppearanceMedium" />

    <SurfaceView
        android:id="@+id/surfaceView"
        android:layout_width="match_parent"
        android:layout_height="200dp" /> <!-- Adjust height as needed -->

    <Button
        android:id="@+id/btnRemoveScreen"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:text="Remove" />
</LinearLayout>