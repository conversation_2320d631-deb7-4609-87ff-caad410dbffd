# 虚拟显示管理系统 (DSMS) 工作原理解析

本文档解析 `MainActivity.java` (Android UI), `VirtualDisplayManagementService.java` (核心服务), 和 `dct_client.py` (远程Python客户端) 三个文件如何协同工作，实现一个Android虚拟显示管理系统。

## 核心目标

该系统的核心目标是：

1.  **手动创建与管理VD**：允许用户在Android应用界面上创建和管理虚拟显示器。
2.  **网络客户端请求VD**：远程Python客户端可通过TCP连接请求并使用虚拟显示器。
3.  **智能复用机制**：网络请求优先复用空闲的手动创建屏幕，提高效率。
4.  **动态按需创建VD**：若无可用手动屏幕或客户端强制，则动态创建新VD。
5.  **状态同步与UI展示**：Android应用界面实时展示各VD的状态（类型、占用情况、ID等）。

## 组件职责

### 1. `VirtualDisplayManagementService.java` (DSMS - 核心服务)

作为系统的后端和大脑，其主要职责包括：

*   **MediaProjection管理**：获取屏幕投射权限和实例，是创建VD的前提。
*   **VD创建与销毁**：调用Android API (`MediaProjection.createVirtualDisplay()`, `VirtualDisplay.release()`)。
*   **VD容器管理**：维护活动VD列表 (`mActiveVirtualDisplays`)，每个条目为 `VirtualDisplayContainer`。
*   **TCP服务器**：启动 `ServerSocket` 监听指定端口，等待客户端连接。
*   **处理客户端请求**：
    *   为每个连接启动 `SmartClientSocketHandler` 线程。
    *   解析客户端请求 (如 `REQUEST_VD_SMART:user_id:force_new`)。
    *   执行VD分配逻辑：尝试复用手动屏幕，或触发创建网络屏幕。
    *   通过 `ActivityCallback` 与 `MainActivity` 交互 (请求UI创建、MP权限)。
    *   将分配结果 (如 `VD_REUSED`, `VD_CREATED`) 通过socket返回客户端。
    *   客户端断开后释放资源 (网络VD或手动VD的网络占用)。
*   **维护网络连接状态**：记录屏幕的网络占用情况 (`mActiveNetworkConnections`)。
*   **与Activity通信**：通过Binder (`LocalBinder`) 和回调接口 (`ActivityCallback`) 进行双向通信。
*   **前台服务运行**：通过通知栏保持服务存活。

### 2. `MainActivity.java` (Android 应用 UI)

作为用户界面和交互层，其主要职责包括：

*   **启动与绑定Service**：在 `onCreate` 中启动并绑定 `VirtualDisplayManagementService`。
*   **实现 `ActivityCallback` 接口**：响应Service的请求：
    *   `onRequestNewVirtualDisplayUI`: 创建新VD的UI元素 (`SurfaceViewItem`)。
    *   `requestMediaProjectionFromUser`: 向用户发起MP权限请求。
    *   `updateManualScreenOccupationStatus`: 更新屏幕占用状态和Display ID的UI。
    *   处理其他通知 (MP繁忙、MP被拒后的重试等)。
*   **处理用户操作**：
    *   **手动添加屏幕**：处理“添加虚拟屏幕”按钮点击，管理MP权限和UI创建。
    *   **移除屏幕**：处理“移除”按钮点击，通知Service释放屏幕并更新UI。
*   **管理 `SurfaceViewItem`**：为每个VD创建和管理包含 `SurfaceView`、信息标签、按钮的UI项。
*   **提供 `Surface`**：当 `SurfaceView` 的 `surfaceCreated` 时，将其 `Surface` 传递给Service。
*   **定期状态检查**：通过 `mStatusCheckRunnable` 定期从Service获取网络屏幕占用信息并更新UI。
*   **MediaProjection权限管理**：处理屏幕捕获权限的请求和结果。

### 3. `dct_client.py` (远程Python客户端)

模拟需要VD资源的远程应用程序，其主要职责包括：

*   **建立TCP连接**：连接到Android设备上DSMS服务的IP和端口。
*   **发送VD请求**：发送特定格式的请求字符串 (如 `REQUEST_VD_SMART:MyPythonApp_123`).
*   **接收Service响应**：处理 `DSMS_CONNECTED`, `VD_PENDING`, `VD_REUSED`, `VD_CREATED`, `ERROR` 等消息。
*   **处理Display ID**：获取 `display_id` 用于后续操作。
*   **保持连接**：获取Display ID后，**必须保持TCP连接打开**，直到不再需要VD。
*   **关闭连接**：完成操作后关闭TCP连接，触发Service端资源释放。

## 原理与工作流程

### 场景1：Python客户端请求VD (智能模式，优先复用)

1.  **启动与绑定 (Android)**：
    *   `MainActivity` 启动并绑定 `VirtualDisplayManagementService`。
    *   Service启动TCP服务器监听。
    *   `ActivityCallback` 注册，状态检查 `Runnable` 开始运行。

2.  **手动屏幕创建 (可选, Android)**：
    *   用户通过UI添加手动屏幕。
    *   `MainActivity` 处理MP权限，创建 `SurfaceViewItem`。
    *   `surfaceCreated` 时，`Surface` 交给Service创建 `VirtualDisplay` (`isManual=true`)。
    *   Service通过回调更新 `MainActivity` UI (Display ID, 空闲状态)。

3.  **Python客户端连接与请求 (Python -> Android Service)**：
    *   `dct_client.py` 与Service建立TCP连接。
    *   Service为连接创建 `SmartClientSocketHandler`。
    *   `SmartClientSocketHandler` 发送 `DSMS_CONNECTED`。
    *   客户端发送 `REQUEST_VD_SMART:PyClient_A`。

4.  **Service处理请求 (Android Service)**：
    *   `SmartClientSocketHandler` 解析请求。
    *   调用 `findAndOccupyReusableManualVD("PyClient_A")`：
        *   **Case A: 找到可复用手动屏幕**：
            *   手动屏幕被标记为网络占用。
            *   连接信息存入 `mActiveNetworkConnections`。
            *   向客户端发送 `VD_REUSED:internal_id:display_id`。
            *   通过回调更新 `MainActivity` UI (屏幕被网络占用)。
        *   **Case B: 未找到可复用手动屏幕**：
            *   向客户端发送 `VD_PENDING`。
            *   **MP权限检查与请求**：
                *   若MP未就绪，通过 `ActivityCallback` 请求 `MainActivity` 发起用户授权。
                *   用户授权后，`MainActivity` 通知Service重试。
                *   用户拒绝，`MainActivity` 通知Service，Service向客户端报错。
            *   **UI创建请求**：若MP就绪，通过 `ActivityCallback` 请求 `MainActivity` 创建新VD的UI (`onRequestNewVirtualDisplayUI`)。
            *   `MainActivity` 创建 `SurfaceViewItem` (`isManuallyCreated=false`)。
            *   `surfaceCreated` 时，`MainActivity` 将 `Surface` 交给Service创建 `VirtualDisplay` (`isManual=false`)。
            *   `MainActivity` 通知Service处理完成 (`notifyVirtualDisplayProcessed`)。
            *   `SmartClientSocketHandler` 获取Display ID。
            *   连接信息存入 `mActiveNetworkConnections`。
            *   向客户端发送 `VD_CREATED:internal_id:display_id`。
            *   通过回调更新 `MainActivity` UI (新网络屏幕被占用)。

5.  **客户端使用与保持连接 (Python & Android Service)**：
    *   Python客户端获取 `display_id`。
    *   客户端**保持TCP连接打开**。
    *   `SmartClientSocketHandler` 阻塞等待客户端断开或进一步数据。

6.  **状态轮询 (Android Activity)**：
    *   `MainActivity` 的 `mStatusCheckRunnable` 定期从Service获取网络屏幕占用信息 (包括连接时长)。
    *   `MainActivity` 更新UI，显示滚动的连接时长。

7.  **客户端断开连接 (Python -> Android Service)**：
    *   Python客户端关闭socket。
    *   `SmartClientSocketHandler` 检测到断开，进入 `finally` 块：
        *   **若复用了手动屏幕**：释放其网络占用，更新 `MainActivity` UI为手动空闲。
        *   **若创建了网络屏幕**：Service释放该网络屏幕的 `VirtualDisplay` 资源和 `VirtualDisplayContainer`，清理 `mActiveNetworkConnections`。(UI项的移除通常由`surfaceDestroyed`触发)
        *   关闭与此客户端相关的socket和IO流。
    *   `SmartClientSocketHandler` 线程结束。

### 场景2：用户在MainActivity中移除手动屏幕

1.  用户点击手动屏幕的“移除”按钮。
2.  `MainActivity` 的 `OnClickListener` 调用 `mService.releaseVirtualDisplay()`。
3.  Service释放该屏幕的 `VirtualDisplay` 资源和 `VirtualDisplayContainer`。
4.  `MainActivity` 从UI和 `mActiveSurfaceViewItems` 中移除对应的 `SurfaceViewItem`。
5.  若为最后一个屏幕，`MainActivity` 停止 `sMediaProjection`。

## 关键原理

*   **解耦**：UI、服务、客户端各司其职，通过明确接口通信。
*   **异步处理**：网络通信、VD创建、权限请求等均异步，通过Handler、线程、回调、Latch协调。
*   **状态管理**：Service维护权威VD状态和网络连接。Activity维护UI状态并与Service同步。
*   **资源生命周期**：
    *   手动屏幕UI生命周期由用户控制，网络占用由客户端连接控制。
    *   网络屏幕UI和资源生命周期与创建它的客户端连接绑定。
    *   MediaProjection生命周期与活动屏幕或服务策略相关。
*   **长连接**：Python客户端获取VD后必须保持TCP连接，这是Service管理资源的关键。

这套流程旨在平衡效率（复用）、灵活性（动态创建）和用户体验（UI反馈）。